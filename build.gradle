// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://plugins.gradle.org/m2/' }
        maven { url "https://jitpack.io" }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:8.1.1"
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:0.14.0'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}