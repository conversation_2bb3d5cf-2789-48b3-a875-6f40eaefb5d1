<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.formats.UnifiedNativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="5dp"
    android:layout_marginRight="5dp"
    android:layout_marginBottom="5dp"
    android:layout_marginTop="5dp"
    android:background="@drawable/native_bg">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:minHeight="30.0dip"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_marginTop="3dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <TextView
                android:visibility="gone"
                android:id="@+id/txt_free"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|left"
                android:layout_marginLeft="10.0dip"
                android:gravity="left"
                android:maxLines="1"
                android:text="FREE"
                android:textColor="#e92e2e"
                android:textSize="15.0sp"
                android:textStyle="bold" />

            <TextView
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|left"
                android:layout_marginLeft="10.0dip"
                android:layout_toRightOf="@+id/txt_free"
                android:gravity="left"
                android:maxLines="1"
                android:text="Google Play"
                android:textColor="#7e7e7e"
                android:textSize="15.0sp"
                android:textStyle="bold" />


        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <RelativeLayout
                android:layout_marginLeft="10.0dip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SPONSORED"
                    android:textColor="#030303"
                    android:textSize="10dp"
                    android:visibility="visible" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/ad_image"
                    android:layout_width="fill_parent"
                    android:layout_height="138.0dip"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="10dp"
                    android:scaleType="fitXY"
                    />

                <com.google.android.gms.ads.formats.MediaView
                    android:id="@+id/ad_media"
                    android:layout_width="fill_parent"
                    android:layout_height="138.0dip"
                    android:layout_marginTop="5dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60.0dip"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal">


                    <ImageView
                        android:id="@+id/ad_app_icon"
                        android:layout_width="55.0dip"
                        android:layout_height="55.0dip"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/ad_headline"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center|left"
                            android:layout_marginLeft="10.0dip"
                            android:gravity="left"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:text="ookkkfghgfhhgvnnnnnnnnnbhvbvvbvbvvb kfijjfn kngkjnfgk ingjnjg iingkj kjngkjngkt kngknk hbrghj"
                            android:textColor="#000000"
                            android:textSize="14.0sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/ad_body"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10.0dip"
                            android:layout_marginTop="5.0dip"
                            android:maxLines="2"
                            android:text="khjkjkg"
                            android:textColor="#7e7e7e"
                            android:textSize="12.0sp"
                            android:visibility="visible" />


                    </LinearLayout>
                </LinearLayout>



                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:orientation="horizontal"
                    android:paddingTop="10.0dip"
                    android:paddingBottom="10.0dip"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/ad_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="5.0dip"
                        android:paddingRight="5.0dip"
                        android:textColor="#ff000000"
                        android:textSize="12.0sp" />

                    <TextView
                        android:id="@+id/ad_store"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="5.0dip"
                        android:paddingRight="5.0dip"
                        android:textColor="#ff000000"
                        android:textSize="12.0sp" />
                </LinearLayout>

                <Button
                    android:id="@+id/ad_call_to_action"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_gravity="center"
                    android:layout_marginTop="5.0dip"
                    android:layout_marginLeft="10.0dip"
                    android:layout_marginRight="10.0dip"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/ad_round"
                    android:gravity="center"
                    android:textColor="#ffffffff"
                    android:textSize="15.0sp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</com.google.android.gms.ads.formats.UnifiedNativeAdView>