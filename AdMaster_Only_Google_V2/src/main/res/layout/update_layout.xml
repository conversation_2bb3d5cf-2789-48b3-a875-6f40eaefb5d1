<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="#ffffff"
    app:cardCornerRadius="2dp"
    app:cardElevation="5dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="30dp"
                />

            <ImageView
                android:id="@+id/cancel"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentRight="true"
                android:padding="10dp"
                android:src="@drawable/close"
                android:tint="#686868" />
        </RelativeLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:gravity="center_horizontal"
            android:text="New Version Available"
            android:paddingLeft="40dp"
            android:paddingRight="40dp"
            android:textColor="#686868"
            android:textSize="21dp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:text="Please update to get latest"
            android:textColor="#686868"
            android:padding="5dp"
            android:textSize="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:text="feature and best experience."
            android:textColor="#686868"
            android:textSize="16dp" />


        <RelativeLayout
            android:id="@+id/update"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="#548F56"
            android:layout_marginTop="30dp">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="UPDATE NOW"
                android:textColor="#ffffff"
                android:layout_centerInParent="true"
                android:textSize="20dp"
                />

        </RelativeLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>