<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="350dp"
    android:layout_gravity="center"
    android:background="#fdd329"
    app:cardBackgroundColor="#fdd329"
    app:cardCornerRadius="10dp"
    app:cardElevation="5dp"
    app:cardUseCompatPadding="true">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="350dp"
        android:layout_gravity="center"
        android:gravity="center">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:src="@drawable/dialog" />

        <ImageView
            android:id="@+id/img_views"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="15dp"
            android:src="@drawable/try_again" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>
