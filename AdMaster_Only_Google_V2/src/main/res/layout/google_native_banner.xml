<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.formats.NativeAppInstallAdView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.0dip"
    android:background="@drawable/native_bg">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="SPONSORED"
        android:textColor="#000000"
        android:textSize="10dp"
        android:layout_marginLeft="10dp"
        android:visibility="visible" />


    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp">

        <LinearLayout
            android:id="@+id/mainlinear"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal">


            <ImageView
                android:id="@+id/appinstall_app_icon"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:adjustViewBounds="true"
                android:layout_marginRight="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:scaleType="centerCrop" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:layout_weight="1.0"
                android:orientation="vertical"
                android:padding="2.0dip">

                <TextView
                    android:id="@+id/appinstall_headline"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="28.0dip"
                    android:maxLines="1"
                    android:lines="1"
                    android:singleLine="true"
                    android:textColor="@android:color/black"
                    android:textSize="15.0sp"
                    android:textStyle="bold" />

                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/appinstall_body"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:maxLines="2"
                        android:textColor="#7e7e7e"
                        android:textSize="12.0sp" />

                </RelativeLayout>

            </LinearLayout>
        </LinearLayout>


        <Button
            android:id="@+id/appinstall_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="35.0dip"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_below="@+id/mainlinear"
            android:background="@drawable/ad_round_native_banner"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:textColor="#ffffffff"
            android:textSize="14.0sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView

                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:textSize="12sp" />

            <ImageView
                android:id="@+id/appinstall_image"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                android:maxHeight="150dp"
                android:paddingTop="5dp"
                android:visibility="gone" />


        </LinearLayout>
    </RelativeLayout>
</com.google.android.gms.ads.formats.NativeAppInstallAdView>
