<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="125dp"
    android:layout_margin="2.0dip"
    android:background="@drawable/native_bg">

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
       >

        <LinearLayout
            android:id="@+id/mainlinear"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal">


            <ImageView
                android:id="@+id/app_icon"
                android:layout_width="68dp"
                android:layout_height="68dp"
                android:layout_marginRight="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:scaleType="fitXY"
                />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="5dp"
                android:layout_weight="1.0"
                android:orientation="vertical"
                android:padding="2.0dip">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/app_title"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="10.0dip"
                        android:maxLines="1"
                        android:lines="1"
                        android:singleLine="true"
                        android:textColor="@android:color/black"
                        android:textSize="15.0sp"
                        android:layout_toLeftOf="@+id/ads"
                        android:textStyle="bold" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/ads"
                        android:layout_width="20dp"
                        android:layout_height="15dp"
                        android:layout_alignParentRight="true"
                        app:cardBackgroundColor="#B6B4B4"
                        app:cardCornerRadius="3dp">

                        <TextView
                            android:layout_width="20dp"
                            android:layout_height="15dp"
                            android:gravity="center_horizontal|center_vertical"
                            android:text="Ad"
                            android:textColor="#000000"
                            android:textSize="10dp"
                            android:visibility="visible" />

                    </androidx.cardview.widget.CardView>
                </RelativeLayout>



                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/app_des"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:maxLines="2"
                        android:textColor="#7e7e7e"
                        android:textSize="12.0sp" />

                </RelativeLayout>

            </LinearLayout>
        </LinearLayout>


        <Button
            android:id="@+id/install_now"
            android:layout_width="match_parent"
            android:layout_height="35.0dip"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_below="@+id/mainlinear"
            android:background="@drawable/ad_round_native_banner"
            android:layout_marginTop="2dp"
            android:layout_marginBottom="2dp"
            android:textColor="#ffffffff"
            android:text="Install Now"
            android:textSize="14.0sp" />

    </RelativeLayout>
</LinearLayout>