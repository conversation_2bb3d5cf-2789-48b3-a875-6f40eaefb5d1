<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ad_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="5dp"
    android:layout_marginTop="5dp"
    android:layout_marginRight="5dp"
    android:layout_marginBottom="5dp"
    android:background="@drawable/native_bg">

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:minHeight="30.0dip"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp">


            <TextView
                android:id="@+id/txt_free"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|left"
                android:layout_marginLeft="10.0dip"
                android:gravity="left"
                android:maxLines="1"
                android:text="FREE"
                android:textColor="#e92e2e"
                android:textSize="15.0sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center|left"
                android:layout_marginLeft="10.0dip"
                android:layout_toRightOf="@+id/txt_free"
                android:gravity="left"
                android:maxLines="1"
                android:text="Google Play"
                android:textColor="#7e7e7e"
                android:textSize="15.0sp"
                android:textStyle="bold"
                android:visibility="gone" />


        </RelativeLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="15dp"
                android:layout_marginLeft="10.0dip"
                android:visibility="gone">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="SPONSORED"
                    android:textColor="#030303"
                    android:textSize="10dp"
                    android:visibility="gone" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/image_banner"
                        android:layout_width="fill_parent"
                        android:layout_height="152.0dip"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:scaleType="centerCrop" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="60.0dip"
                    android:layout_marginLeft="10dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="10dp"
                    android:orientation="horizontal">


                    <ImageView
                        android:id="@+id/ad_app_icon"
                        android:layout_width="55.0dip"
                        android:layout_height="55.0dip"
                        android:adjustViewBounds="true"
                        android:scaleType="fitXY" />

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:orientation="vertical">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <TextView
                                android:id="@+id/ad_title"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center|left"
                                android:layout_marginLeft="10.0dip"
                                android:gravity="left"
                                android:maxLines="1"
                                android:singleLine="true"
                                android:text="ookkkfghgfhhgvnnnnnnnnnbhvbvvbvbvvb kfijjfn kngkjnfgk ingjnjg iingkj kjngkjngkt kngknk hbrghj"
                                android:textColor="#000000"
                                android:layout_toLeftOf="@+id/ads"
                                android:textSize="14.0sp"
                                android:textStyle="bold" />

                            <androidx.cardview.widget.CardView
                                android:id="@+id/ads"
                                android:layout_width="20dp"
                                android:layout_height="15dp"
                                android:layout_alignParentRight="true"
                                app:cardBackgroundColor="#B6B4B4"
                                app:cardCornerRadius="3dp">

                                <TextView

                                    android:layout_width="20dp"
                                    android:layout_height="15dp"
                                    android:gravity="center_horizontal|center_vertical"
                                    android:text="Ad"
                                    android:textColor="#000000"
                                    android:textSize="10dp"
                                    android:visibility="visible" />

                            </androidx.cardview.widget.CardView>



                        </RelativeLayout>


                        <TextView
                            android:id="@+id/ad_des"
                            android:layout_width="fill_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="10.0dip"
                            android:layout_marginTop="5.0dip"
                            android:maxLines="2"
                            android:text="khjkjkg"
                            android:textColor="#7e7e7e"
                            android:textSize="12.0sp"
                            android:visibility="visible" />


                    </LinearLayout>
                </LinearLayout>

                <Button
                    android:id="@+id/install_now"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10.0dip"
                    android:layout_marginTop="5.0dip"
                    android:layout_marginRight="10.0dip"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/ad_round"
                    android:gravity="center"
                    android:text="Install Now"
                    android:textColor="#ffffffff"
                    android:textSize="15.0sp" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>