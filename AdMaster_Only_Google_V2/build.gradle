apply plugin: 'com.android.library'

android {
    compileSdkVersion 34
//    buildToolsVersion "29.0.0"
    namespace 'com.ru.admaster'


    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'androidx.appcompat:appcompat:1.6.1'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.1'

    implementation 'com.google.android.gms:play-services-ads:19.4.0'

    implementation 'com.android.volley:volley:1.2.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'com.github.javiersantos:AppUpdater:2.7'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'
    implementation 'com.google.android.material:material:1.11.0'
}
