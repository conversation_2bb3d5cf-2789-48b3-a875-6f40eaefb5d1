plugins {
    id 'com.android.application'
    id 'com.onesignal.androidsdk.onesignal-gradle-plugin'
    id 'com.google.firebase.crashlytics'
}

android {
    namespace 'com.camscanner.docscanner.pdfcreator'
    compileSdk 34

    defaultConfig {
        applicationId "com.camscanner.docscanner.pdfcreator"
        minSdk 16
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
        manifestPlaceholders = [
                onesignal_app_id               : '************************************',
                onesignal_google_project_number: 'REMOTE'
        ]
    }

    buildTypes {
        release {
            minifyEnabled true
            pseudoLocalesEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            shrinkResources true
            zipAlignEnabled true
        }
        debug {
            minifyEnabled true
            pseudoLocalesEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            shrinkResources true
            zipAlignEnabled true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}
dependencies {
    implementation project(':AdMaster_Only_Google_V2')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.multidex:multidex:2.0.1'

    // Camera and image processing
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    // PDF and document processing
    implementation 'com.itextpdf:itextpdf:5.5.13.3'

    // Image picker and cropping
    implementation 'com.github.yalantis:ucrop:2.2.8'

    // Network and HTTP
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Testing dependencies
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}