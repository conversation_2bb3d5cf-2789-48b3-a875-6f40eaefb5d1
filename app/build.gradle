plugins {
    id 'com.android.application'
    id 'com.onesignal.androidsdk.onesignal-gradle-plugin'
    id 'com.google.firebase.crashlytics'
}

android {
    namespace 'com.camscanner.docscanner.pdfcreator'
    compileSdk 34

    defaultConfig {
        applicationId "com.camscanner.docscanner.pdfcreator"
        minSdk 16
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a"
        }
        manifestPlaceholders = [
                onesignal_app_id               : '************************************',
                onesignal_google_project_number: 'REMOTE'
        ]
    }

    buildTypes {
        release {
            minifyEnabled true
            pseudoLocalesEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            shrinkResources true
            zipAlignEnabled true
        }
        debug {
            minifyEnabled true
            pseudoLocalesEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            shrinkResources true
            zipAlignEnabled true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        viewBinding true
    }
}
dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
}