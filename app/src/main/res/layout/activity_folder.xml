<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/appbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/MainAppTheme.AppBarOverlay">

            <FrameLayout
                android:id="@+id/toolbar_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    android:background="@color/colorPrimary"
                    app:contentInsetEnd="0dp"
                    app:contentInsetLeft="0dp"
                    app:contentInsetRight="0dp"
                    app:contentInsetStart="0dp"
                    app:popupTheme="@style/MainAppTheme.PopupOverlay">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <ImageView
                            android:id="@+id/btn_menu"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginLeft="8dp"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:clickable="true"
                            android:focusable="true"
                            android:padding="8dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:srcCompat="@drawable/ic_back_long" />

                        <TextView
                            android:id="@+id/title"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginLeft="4dp"
                            android:layout_marginRight="16dp"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:paddingTop="4dp"
                            android:paddingBottom="4dp"
                            android:textColor="@color/colorWhite"
                            android:textSize="18sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/btn_menu"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </androidx.appcompat.widget.Toolbar>

                <com.miguelcatalan.materialsearchview.MaterialSearchView
                    android:id="@+id/search_view"
                    style="@style/MaterialSearchViewStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <include
                    android:id="@+id/multi_select_bar"
                    layout="@layout/multi_select_bar" />
            </FrameLayout>
        </com.google.android.material.appbar.AppBarLayout>
    </LinearLayout>

    <include
        android:id="@+id/secondbar"
        layout="@layout/second_tool_bar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar" />

    <ImageView
        android:id="@+id/ivEmptyBG"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:scaleType="fitEnd"
        app:layout_constraintBottom_toBottomOf="@+id/tvStartScan"
        app:layout_constraintHeight_percent="0.6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintWidth_percent="0.6"
        app:srcCompat="@drawable/bg_placeholder" />

    <include
        android:id="@+id/multi_select_bottom_bar"
        layout="@layout/multi_select_bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/footer_size"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ListView
        android:id="@+id/document_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:divider="@color/colorDivider"
        android:dividerHeight="1dp"
        android:scrollbars="none"
        android:footerDividersEnabled="true"
        app:layout_constraintBottom_toTopOf="@+id/multi_select_bottom_bar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/secondbar"
        app:layout_constraintWidth_default="wrap" />

    <TextView
        android:id="@+id/tvStartScan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/str_start_scanning"
        android:textColor="#fff"
        android:textSize="18sp"
        app:layout_constraintBottom_toTopOf="@+id/rlBottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rlBottom"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/fab_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <ImageView
            android:id="@+id/btn_camera"
            android:layout_width="@dimen/fab_big"
            android:layout_height="@dimen/fab_big"
            android:background="@drawable/bg_floating_button"
            android:clickable="true"
            android:focusable="true"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:srcCompat="@drawable/ic_camera" />

        <ImageView
            android:id="@+id/btn_gallery"
            android:layout_width="@dimen/fab_small"
            android:layout_height="@dimen/fab_small"
            android:layout_marginLeft="16dp"
            android:background="@drawable/bg_floating_button"
            android:clickable="true"
            android:contentDescription="@string/action_import_from_gallery"
            android:focusable="true"
            android:gravity="center"
            android:padding="12dp"
            app:layout_constraintBottom_toBottomOf="@+id/btn_camera"
            app:layout_constraintLeft_toRightOf="@+id/btn_camera"
            app:layout_constraintTop_toTopOf="@+id/btn_camera"
            app:srcCompat="@drawable/ic_gallery" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
