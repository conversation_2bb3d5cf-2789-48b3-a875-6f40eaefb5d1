<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialog"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        app:cardBackgroundColor="@color/appbid_white"
        app:cardCornerRadius="8dp"
        app:cardUseCompatPadding="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="@dimen/appbid_margin_side_title"
                android:layout_marginTop="12dp"
                app:layout_constraintEnd_toStartOf="@+id/title"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="@dimen/appbid_margin_side_title"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:textColor="@color/appbid_black_text"
                android:textSize="18sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/logo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/logo"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toEndOf="@+id/logo"
                app:layout_constraintTop_toTopOf="@+id/logo" />

            <TextView
                android:id="@+id/text_first"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="8dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:gravity="center"
                android:textColor="@color/appbid_gray_text"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/logo" />

            <ScrollView
                android:id="@+id/scroll_flow"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:visibility="gone"
                app:layout_constrainedHeight="true"
                app:layout_constraintHeight_max="@dimen/appbid_flow_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/text_first">

                <com.nex3z.flowlayout.FlowLayout
                    android:id="@+id/providers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:layout_marginRight="8dp"
                    app:flChildSpacing="2dp"
                    app:flFlow="true"
                    app:flRowSpacing="4dp" />
            </ScrollView>

            <ProgressBar
                android:id="@+id/progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:theme="@style/ProgressStyle"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/text_first" />

            <View
                android:id="@+id/divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="4dp"
                android:background="@color/appbid_selected"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/scroll_flow"
                app:layout_goneMarginTop="56dp" />

            <TextView
                android:id="@+id/learn_how"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingTop="2dp"
                android:paddingRight="8dp"
                android:paddingBottom="2dp"
                android:textColor="@color/appbid_primaryColor"
                android:textSize="15sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider" />

            <TextView
                android:id="@+id/back_button"
                style="@style/Button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="16dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:layout_marginBottom="12dp"
                android:paddingLeft="42dp"
                android:paddingTop="8dp"
                android:paddingRight="24dp"
                android:paddingBottom="8dp"
                android:text="@string/appbid_back"
                android:textSize="17sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/learn_how" />

            <ImageView
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginLeft="4dp"
                app:layout_constraintBottom_toBottomOf="@+id/back_button"
                app:layout_constraintLeft_toLeftOf="@+id/back_button"
                app:layout_constraintStart_toStartOf="@+id/back_button"
                app:layout_constraintTop_toTopOf="@+id/back_button"
                app:srcCompat="@drawable/ic_dialog_back" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
