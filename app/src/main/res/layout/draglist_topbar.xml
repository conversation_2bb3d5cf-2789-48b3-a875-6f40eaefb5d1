<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_topbar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/delete_area_height"
    android:background="@color/colorBackgroundFooter">

    <ImageView
        android:id="@+id/iv_remove"
        android:layout_width="37dp"
        android:layout_height="40dp"
        app:layout_constraintBottom_toTopOf="@+id/tv_remove"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="spread"
        app:srcCompat="@drawable/ic_drag_delete_off" />

    <TextView
        android:id="@+id/tv_remove"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:fontFamily="sans-serif-medium"
        android:text="@string/drag_here_to_remove"
        android:textColor="@color/colorTextScanner"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_remove" />
</androidx.constraintlayout.widget.ConstraintLayout>
