<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="blocksDescendants">

    <RelativeLayout
        android:id="@+id/rl_lang_ar"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?attr/selectableItemBackground">

        <TextView
            android:id="@+id/tv_lang_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:paddingLeft="20dp"
            android:text="@string/setting_language_ar"
            android:textColor="@color/colorSettingItemColor2" />

        <ImageView
            android:id="@+id/iv_lang_check"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            app:srcCompat="@drawable/ic_check" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:layout_marginLeft="15dp"
            android:background="@color/colorBGGray" />
    </RelativeLayout>
</RelativeLayout>
