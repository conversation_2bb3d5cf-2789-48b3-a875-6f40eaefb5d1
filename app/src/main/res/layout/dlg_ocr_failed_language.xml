<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <View
        android:id="@+id/bottom_before"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@android:color/transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/dialog_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:visibility="invisible"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/bottom_before">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/ocr_fail_title"
                android:textColor="@color/black87"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="20dp"
                android:gravity="center"
                android:text="@string/ocr_fail_message"
                android:textColor="@color/colorTextScanner"
                android:textSize="14sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title" />

            <ImageView
                android:id="@+id/image"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="16dp"
                app:layout_constraintBottom_toTopOf="@+id/btn_ok"
                app:layout_constraintDimensionRatio="W,1192:1000"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/message"
                app:layout_constraintWidth_percent="0.243"
                app:srcCompat="@drawable/ic_ocr_dialog_failed" />

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:background="@drawable/bg_white_btn"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="@string/str_cancel"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/btn_ok"
                app:layout_constraintTop_toBottomOf="@+id/image"
                app:layout_goneMarginTop="20dp" />

            <TextView
                android:id="@+id/btn_ok"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:background="@drawable/bg_white_btn"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:text="@string/ocr_fail_ok"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/btn_cancel"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/image"
                app:layout_goneMarginTop="20dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:background="@color/colorBackgroundFooter"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/btn_ok" />

            <View
                android:layout_width="1dp"
                android:layout_height="0dp"
                android:background="@color/colorBackgroundFooter"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@+id/btn_ok"
                app:layout_constraintTop_toTopOf="@+id/btn_ok" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
