<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/root" android:background="#929c9f" android:layout_width="match_parent" android:layout_height="match_parent">
    <com.google.android.material.appbar.AppBarLayout android:id="@+id/appbar" android:focusable="true" android:focusableInTouchMode="true" android:layout_width="0dp" android:layout_height="56dp" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="match_parent" android:layout_height="match_parent">
            <ImageView android:id="@+id/btn_back" android:background="?attr/selectableItemBackgroundBorderless" android:padding="8dp" android:focusable="true" android:clickable="true" android:layout_width="40dp" android:layout_height="40dp" android:layout_marginLeft="8dp" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/ic_back_long"/>
            <TextView android:textSize="18sp" android:textColor="@color/whiteTitle" android:id="@+id/title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/qr_scan" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent"/>
            <ImageView android:id="@+id/btn_flash" android:background="?attr/selectableItemBackgroundBorderless" android:paddingLeft="3dp" android:paddingTop="2dp" android:paddingRight="3dp" android:paddingBottom="8dp" android:focusable="true" android:clickable="true" android:layout_width="40dp" android:layout_height="40dp" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toLeftOf="@+id/btn_list" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/ic_qr_flash_on"/>
            <ImageView android:id="@+id/btn_list" android:background="?attr/selectableItemBackgroundBorderless" android:padding="8dp" android:focusable="true" android:clickable="true" android:layout_width="40dp" android:layout_height="40dp" android:layout_marginRight="8dp" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/ic_qr_history"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
