<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <View
        android:id="@+id/bottom_after"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <View
        android:id="@+id/bottom_before"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="200dp"
        android:background="@android:color/transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="parent" />

    <View
        android:id="@+id/bottom_end"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="250dp"
        android:background="@android:color/transparent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="parent" />

    <FrameLayout
        android:id="@+id/dialog_root"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/bottom_before"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/dialog"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="-16dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/title_export"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:text="@string/export_document_as"
                    android:textColor="@color/black87"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@+id/top_divider"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/tabs"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tabs"
                    android:layout_width="180dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/top_divider"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tabGravity="center"
                    app:tabIndicatorColor="@color/colorPrimary"
                    app:tabIndicatorHeight="6dp"
                    app:tabMode="auto"
                    app:tabSelectedTextColor="@color/colorPrimary"
                    app:tabTextColor="@color/colorTextScanner" />

                <View
                    android:id="@+id/top_divider"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginTop="56dp"
                    android:background="@color/colorDivider"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/title_quality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginTop="24dp"
                    android:text="@string/set_quality"
                    android:textColor="@color/black87"
                    android:textSize="15sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/top_divider" />

                <View
                    android:id="@+id/title_quality_center"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    app:layout_constraintBottom_toBottomOf="@+id/title_quality"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/title_quality" />

                <com.camscanner.docscanner.pdfcreator.common.views.stepslider.StepSlider
                    android:id="@+id/slider_quality"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingLeft="24dp"
                    android:paddingRight="24dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/title_quality_center"
                    app:ss_margin_text="18dp"
                    app:ss_position="2"
                    app:ss_prem_src="@drawable/trans"
                    app:ss_step="3"
                    app:ss_str_array="@array/setting_share_quality_array"
                    app:ss_text_color="@color/colorTextScanner"
                    app:ss_text_selected_color="@color/colorPrimary"
                    app:ss_text_size_deselected="12sp"
                    app:ss_text_size_selected="12sp"
                    app:ss_thumb_bg_color="@color/colorPrimary"
                    app:ss_thumb_bg_radius="10dp"
                    app:ss_thumb_radius="14dp"
                    app:ss_track_bg_height="3dp"
                    app:ss_track_color="@color/colorPrimary" />

                <TextView
                    android:id="@+id/btn_export"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="24dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginRight="24dp"
                    android:layout_marginBottom="40dp"
                    android:background="@drawable/background_button_share"
                    android:gravity="center"
                    android:paddingTop="16dp"
                    android:paddingBottom="16dp"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/slider_quality" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>
    </FrameLayout>

    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:padding="6dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/dialog_root"
        app:layout_constraintRight_toRightOf="@+id/dialog_root"
        app:layout_constraintTop_toTopOf="@+id/dialog_root"
        app:srcCompat="@drawable/ic_export_close" />
</androidx.constraintlayout.widget.ConstraintLayout>
