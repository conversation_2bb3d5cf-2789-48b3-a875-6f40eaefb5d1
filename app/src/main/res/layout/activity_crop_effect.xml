<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_cropEffect"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/footer_toolbar_height"
        android:background="@color/colorPrimary"
        android:orientation="vertical">

        <ProgressBar
            android:id="@+id/pb_cropState"
            style="@style/Base.Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:indeterminate="false"
            android:max="10"
            android:theme="@style/ProgressBar" />

        <TextView
            android:id="@+id/txt_cropState"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_crop_image"
            android:textColor="@color/colorWhite"
            android:textSize="@dimen/shared_text_size" />
    </LinearLayout>
</LinearLayout>
