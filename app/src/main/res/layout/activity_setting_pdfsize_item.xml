<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/MainAppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:popupTheme="@style/MainAppTheme.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/appbar"
        android:background="@color/colorSettingBackground1"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/colorSettingBackground1" />

        <RelativeLayout
            android:id="@+id/rl_pdf_size_item_name"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/colorSettingBackground2"
            android:clickable="true">

            <EditText
                android:id="@+id/et_pdf_size_item_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:background="@null"
                android:contentDescription="@string/hint_pdf_size_name"
                android:hint="@string/hint_pdf_size_name"
                android:textSize="18sp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/colorSettingBackground1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/colorSettingBackground2"
            android:clickable="true">

            <EditText
                android:id="@+id/et_pdf_size_item_width"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:background="@null"
                android:contentDescription="@string/hint_pdf_size_width"
                android:hint="@string/hint_pdf_size_width"
                android:inputType="number"
                android:textSize="18sp" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:background="@color/colorSettingBackground1" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/colorSettingBackground2"
            android:clickable="true">

            <EditText
                android:id="@+id/et_pdf_size_item_height"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:background="@null"
                android:contentDescription="@string/hint_pdf_size_height"
                android:hint="@string/hint_pdf_size_height"
                android:inputType="number"
                android:textSize="18sp" />
        </RelativeLayout>
    </LinearLayout>
</RelativeLayout>
