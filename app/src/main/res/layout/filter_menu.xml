<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" android:id="@+id/root" android:background="?attr/selectableItemBackground" android:focusable="true" android:clickable="true" android:layout_width="@dimen/filter_toolbar_height" android:layout_height="@dimen/filter_toolbar_height">
    <ImageView android:id="@+id/iv_filter" android:layout_width="match_parent" android:layout_height="match_parent" android:scaleType="fitCenter"/>
    <TextView android:textSize="@dimen/list_document_doc_text_size" android:textColor="@android:color/white" android:gravity="center" android:id="@+id/txt_filter_name" android:background="@color/colorGray" android:layout_width="match_parent" android:layout_height="20dp" android:layout_alignParentBottom="true" android:alpha="0.7"/>
</RelativeLayout>
