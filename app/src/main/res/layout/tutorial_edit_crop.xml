<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guid_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="48dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guid_vert"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.3" />

    <io.codetail.widget.RevealFrameLayout
        android:id="@+id/frame"
        android:layout_width="800dp"
        android:layout_height="700dp"
        app:layout_constraintBottom_toBottomOf="@+id/guid_horizontal"
        app:layout_constraintLeft_toLeftOf="@+id/guid_vert"
        app:layout_constraintRight_toRightOf="@+id/guid_vert"
        app:layout_constraintTop_toTopOf="@+id/guid_horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/reveal_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/background"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                android:src="@drawable/background_tutorial"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/background_small"
                android:layout_width="94dp"
                android:layout_height="94dp"
                android:src="@drawable/background_tutorial_mini"
                app:layout_constraintBottom_toBottomOf="@+id/background_white"
                app:layout_constraintLeft_toLeftOf="@+id/background_white"
                app:layout_constraintRight_toRightOf="@+id/background_white"
                app:layout_constraintTop_toTopOf="@+id/background_white" />

            <ImageView
                android:id="@+id/background_white"
                android:layout_width="68dp"
                android:layout_height="68dp"
                android:src="@drawable/background_tutorial_white"
                app:layout_constraintBottom_toBottomOf="@+id/btn_crop"
                app:layout_constraintLeft_toLeftOf="@+id/btn_crop"
                app:layout_constraintRight_toRightOf="@+id/btn_crop"
                app:layout_constraintTop_toTopOf="@+id/btn_crop" />

            <ImageView
                android:id="@+id/btn_crop"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_edit_crop" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guid_vert_inside"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.42" />

            <TextView
                android:id="@+id/message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="56dp"
                android:text="@string/tutorial_message_edit_crop"
                android:textColor="@color/white"
                android:textSize="15sp"
                app:layout_constraintBottom_toTopOf="@+id/background_small"
                app:layout_constraintLeft_toLeftOf="@+id/guid_vert_inside" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="@string/tutorial_title_edit_crop"
                android:textColor="@color/white"
                android:textSize="22sp"
                app:layout_constraintBottom_toTopOf="@+id/message"
                app:layout_constraintLeft_toLeftOf="@+id/message" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </io.codetail.widget.RevealFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
