<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rl_topbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        android:padding="10dp">

        <ImageView
            android:id="@+id/iv_date_cancel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            app:srcCompat="@drawable/icon_cancel" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/str_sign_date"
            android:textColor="@color/colorWhite"
            android:textSize="18sp" />

        <ImageView
            android:id="@+id/iv_date_done"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:adjustViewBounds="true"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            app:srcCompat="@drawable/ic_done" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_date_preview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/colorBlack"
            android:textSize="24sp" />
    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_above="@+id/ll_bottombar"
        android:background="@color/color_signature_border" />

    <LinearLayout
        android:id="@+id/ll_color_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />

        <RelativeLayout
            android:id="@+id/rl_date_color_blue"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/bg_sign_color_blue_20dp" />
        </RelativeLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />

        <RelativeLayout
            android:id="@+id/rl_date_color_red"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/bg_sign_color_red_20dp" />
        </RelativeLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />

        <RelativeLayout
            android:id="@+id/rl_date_color_black"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/bg_sign_color_black_20dp" />
        </RelativeLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:background="@color/color_signature_border" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />

        <RelativeLayout
            android:id="@+id/rl_pick_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/str_pick_date"
                android:textColor="@color/colorBlack" />
        </RelativeLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />

        <RelativeLayout
            android:id="@+id/rl_date_format"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="15dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/str_date_format"
                android:textColor="@color/colorBlack" />
        </RelativeLayout>

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/color_signature_border" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_alignParentBottom="true"
        android:background="@color/color_signature_border" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.8">

        <com.tsongkha.spinnerdatepicker.DatePicker
            android:id="@+id/sdp_picker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:padding="10dp" />

        <ListView
            android:id="@+id/lv_date_format"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />
    </RelativeLayout>
</LinearLayout>
