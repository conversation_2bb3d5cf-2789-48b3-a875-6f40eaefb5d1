<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:orientation="horizontal" android:id="@+id/root" android:layout_width="match_parent" android:layout_height="36dp">
    <ImageView android:layout_gravity="center" android:id="@+id/iv_folder" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="20dp" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/ic_folder_new"/>
    <TextView android:textSize="14sp" android:textColor="@color/colorTextScanner" android:ellipsize="end" android:gravity="left" android:id="@+id/tv_folder_name" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_marginLeft="16dp" android:singleLine="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toRightOf="@+id/iv_folder" app:layout_constraintRight_toRightOf="parent" app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
