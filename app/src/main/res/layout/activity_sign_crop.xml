<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/MainAppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/MainAppTheme.PopupOverlay">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_sign_crop_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:background="?attr/selectableItemBackground"
                    android:padding="10dp"
                    android:text="@string/str_sign_cancel"
                    android:textColor="@color/colorWhite" />

                <TextView
                    android:id="@+id/tv_sign_crop_done"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="20dp"
                    android:background="?attr/selectableItemBackground"
                    android:padding="10dp"
                    android:text="@string/str_sign_done"
                    android:textColor="@color/colorWhite" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/str_sign_crop_title"
                    android:textAllCaps="true"
                    android:textColor="@color/colorWhite"
                    android:textStyle="bold" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <com.camscanner.docscanner.pdfcreator.common.views.simplecropview.SignatureCropImageView
        android:id="@+id/sgcv_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/appbar"
        app:scv_handle_size="12dp"
        app:scv_overlay_color="@color/overlay"
        app:scv_touch_padding="8dp" />
</RelativeLayout>
