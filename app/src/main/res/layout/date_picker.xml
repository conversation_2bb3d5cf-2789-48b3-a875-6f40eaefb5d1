<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" android:layout_gravity="center_horizontal" android:orientation="vertical" android:layout_width="270dp" android:layout_height="wrap_content">
    <LinearLayout android:layout_gravity="center_horizontal" android:orientation="horizontal" android:id="@+id/parent" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <NumberPicker android:id="@+id/month" android:focusable="true" android:focusableInTouchMode="true" android:layout_width="80dp" android:layout_height="wrap_content" android:layout_marginLeft="1dp" android:layout_marginRight="1dp"/>
        <NumberPicker android:id="@+id/day" android:focusable="true" android:focusableInTouchMode="true" android:layout_width="80dp" android:layout_height="wrap_content" android:layout_marginLeft="1dp" android:layout_marginRight="1dp"/>
        <NumberPicker android:id="@+id/year" android:focusable="true" android:focusableInTouchMode="true" android:layout_width="95dp" android:layout_height="wrap_content" android:layout_marginLeft="1dp" android:layout_marginRight="1dp"/>
    </LinearLayout>
</LinearLayout>
