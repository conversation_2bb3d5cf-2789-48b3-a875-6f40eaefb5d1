<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@color/colorPrimary">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/btn_back"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="10dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:srcCompat="@drawable/ic_back_long" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:text="@string/app_name"
                    android:textColor="@color/white"
                    android:textSize="18dp"></TextView>

                <ImageView
                    android:id="@+id/btn_share"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:layout_gravity="center_vertical"
                    android:layout_margin="10dp"
                    android:clickable="true"
                    android:focusable="true"
                    app:srcCompat="@drawable/ic_share_new" />
            </LinearLayout>

        </LinearLayout>


        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.ortiz.touchview.TouchImageView
                    android:id="@+id/iv_croped"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="matrix"
                    app:layout_constraintBottom_toTopOf="@+id/footer"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <LinearLayout
                    android:id="@+id/footer"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/footer_height"
                    android:background="@color/colorBackgroundFooter"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/btn_sign"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/background_button_tool"
                        android:gravity="center"
                        android:minHeight="?attr/actionBarSize"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/image_sign"
                            android:layout_width="@dimen/size_footer_icon_edit"
                            android:layout_height="@dimen/size_footer_icon_edit"
                            android:layout_gravity="center"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="0.3"
                            app:srcCompat="@drawable/ic_edit_sign" />

                        <TextView
                            style="@style/EditFooterText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/size_footer_text_margin"
                            android:text="@string/edit_footer_sign"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/image_sign" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/btn_edit"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/background_button_tool"
                        android:gravity="center"
                        android:minHeight="?attr/actionBarSize"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/image_edit"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center"
                            android:padding="10dp"
                            android:tint="@color/colorPrimary"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="0.3"
                            app:srcCompat="@drawable/ic_edit_new" />

                        <TextView
                            style="@style/EditFooterText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/size_footer_text_margin"
                            android:text="Edit"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/image_edit" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/btn_crop"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/background_button_tool"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/image_crop"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center"
                            android:padding="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="0.3"
                            app:srcCompat="@drawable/ic_crop" />

                        <TextView
                            style="@style/EditFooterText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/size_footer_text_margin"
                            android:text="@string/crop"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/image_crop" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/btn_ocr"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/background_button_tool"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/image_ocr"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center"
                            android:padding="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="0.3"
                            app:srcCompat="@drawable/ic_edit_ocr" />

                        <TextView
                            style="@style/EditFooterText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/size_footer_text_margin"
                            android:text="@string/str_ocr_recognize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/image_ocr" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/btn_delete"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/background_button_tool"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/image_remove"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_gravity="center"
                            android:padding="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="0.3"
                            app:srcCompat="@drawable/ic_delete" />

                        <TextView
                            style="@style/EditFooterText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/size_footer_text_margin"
                            android:text="@string/str_delete"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/image_remove" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/btn_retake"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/background_button_tool"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:id="@+id/image_retake"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:padding="10dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintVertical_bias="0.3"
                            app:srcCompat="@drawable/ic_edit_retake" />

                        <TextView
                            style="@style/EditFooterText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginTop="@dimen/size_footer_text_margin"
                            android:text="@string/str_retake"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/image_retake" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/relative_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">

    </LinearLayout>
</LinearLayout>
