<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:layout_width="match_parent" android:layout_height="match_parent">
    <com.google.android.material.appbar.AppBarLayout android:theme="@style/MainAppTheme.AppBarOverlay" android:id="@+id/appbar" android:layout_width="match_parent" android:layout_height="wrap_content">
        <androidx.appcompat.widget.Toolbar android:id="@+id/toolbar" android:background="@color/colorPrimary" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:popupTheme="@style/MainAppTheme.PopupOverlay"/>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout android:orientation="vertical" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_below="@+id/appbar">
        <RelativeLayout android:background="@color/colorSettingBackground1" android:layout_width="match_parent" android:layout_height="56dp">
            <TextView android:paddingLeft="10dp" android:paddingBottom="5dp" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/setting_language" android:layout_alignParentBottom="true" android:layout_centerVertical="true" style="@style/SettingSubtitleText"/>
            <View android:background="@color/colorBGGray" android:layout_width="match_parent" android:layout_height="1dp" android:layout_alignParentBottom="true"/>
        </RelativeLayout>
        <ListView android:id="@+id/lv_language" android:layout_width="match_parent" android:layout_height="match_parent" android:divider="@null"/>
    </LinearLayout>
</RelativeLayout>
