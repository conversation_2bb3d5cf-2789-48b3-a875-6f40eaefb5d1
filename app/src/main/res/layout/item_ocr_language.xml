<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:id="@+id/root" android:background="@drawable/background_button_ocr" android:layout_width="match_parent" android:layout_height="48dp">
    <TextView android:textSize="17sp" android:textColor="@color/colorTextScanner" android:id="@+id/text_language" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="20dp" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintLeft_toLeftOf="parent" app:layout_constraintTop_toTopOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
