<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/doc_background"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="56dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_back_long" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:layout_marginRight="4dp"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:textColor="@color/whiteTitle"
                android:textSize="18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/btn_back"
                app:layout_constraintRight_toLeftOf="@+id/btn_edit"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btn_edit"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/btn_settings"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_edit_new" />

            <ImageView
                android:id="@+id/btn_settings"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/btn_save"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_settings" />

            <ImageView
                android:id="@+id/btn_save"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginRight="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_save_new" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/grid_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/prl_bottom_bar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="bottom">

            <com.camscanner.docscanner.pdfcreator.common.views.draglistview.DragListView
                android:id="@+id/drag_list_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rlBottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/fab_margin"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">


            <ImageView
                android:id="@+id/btn_export"
                android:layout_width="@dimen/fab_small"
                android:layout_height="@dimen/fab_small"
                android:layout_marginRight="16dp"
                android:background="@drawable/bg_floating_button"
                android:clickable="true"
                android:contentDescription="@string/action_import_from_gallery"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/btn_camera"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintLeft_toLeftOf="@+id/parent"
                app:layout_constraintRight_toLeftOf="@+id/btn_camera"
                app:layout_constraintTop_toTopOf="@+id/btn_camera"
                app:srcCompat="@drawable/ic_share_new" />


            <ImageView
                android:id="@+id/btn_camera"
                android:layout_width="@dimen/fab_big"
                android:layout_height="@dimen/fab_big"
                android:background="@drawable/bg_floating_button"
                android:clickable="true"
                android:focusable="true"
                android:padding="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:srcCompat="@drawable/ic_camera" />


            <ImageView
                android:id="@+id/btn_gallery"
                android:layout_width="@dimen/fab_small"
                android:layout_height="@dimen/fab_small"
                android:layout_marginLeft="16dp"
                android:background="@drawable/bg_floating_button"
                android:clickable="true"
                android:contentDescription="@string/action_import_from_gallery"
                android:focusable="true"
                android:gravity="center"
                android:padding="12dp"
                app:layout_constraintBottom_toBottomOf="@+id/btn_camera"
                app:layout_constraintLeft_toRightOf="@+id/btn_camera"
                app:layout_constraintTop_toTopOf="@+id/btn_camera"
                app:srcCompat="@drawable/ic_gallery" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>
