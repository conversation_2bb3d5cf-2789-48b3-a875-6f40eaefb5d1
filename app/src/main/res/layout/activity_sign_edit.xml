<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/background_images"
        app:layout_constraintBottom_toTopOf="@+id/bottombar"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="@dimen/fab_medium"
        android:layout_height="@dimen/fab_medium"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/margin_side_top_btn"
        android:layout_marginTop="@dimen/margin_top_top_btn"
        android:background="@drawable/bg_floating_button"
        android:clickable="true"
        android:focusable="true"
        android:padding="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_back_long" />

    <ImageView
        android:id="@+id/btn_done"
        android:layout_width="@dimen/fab_medium"
        android:layout_height="@dimen/fab_medium"
        android:layout_centerInParent="true"
        android:layout_marginTop="20dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/bg_floating_button"
        android:clickable="true"
        android:focusable="true"
        android:padding="14dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_done" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottombar"
        android:layout_width="0dp"
        android:layout_height="@dimen/footer_height"
        android:background="@color/colorBackgroundFooter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_signature"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/btn_date"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_signature"
                android:layout_width="@dimen/size_footer_icon_sign"
                android:layout_height="@dimen/size_footer_icon_sign"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_sign_create" />

            <TextView
                style="@style/SignFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/edit_footer_sign"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_signature" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_date"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_signature"
            app:layout_constraintRight_toLeftOf="@+id/btn_text"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_date"
                android:layout_width="@dimen/size_footer_icon_sign"
                android:layout_height="@dimen/size_footer_icon_sign"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_sign_date" />

            <TextView
                style="@style/SignFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/str_sign_date"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_date" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_date"
            app:layout_constraintRight_toLeftOf="@+id/btn_freestyle"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_text"
                android:layout_width="@dimen/size_footer_icon_sign"
                android:layout_height="@dimen/size_footer_icon_sign"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_sign_text" />

            <TextView
                style="@style/SignFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/str_sign_text"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_text" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_freestyle"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_button_tool"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_text"
            app:layout_constraintRight_toLeftOf="@+id/btn_checkbox"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_free"
                android:layout_width="@dimen/size_footer_icon_sign"
                android:layout_height="@dimen/size_footer_icon_sign"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_sign_free" />

            <TextView
                style="@style/SignFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/str_sign_freestytle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_free" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_checkbox"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_button_tool"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_freestyle"
            app:layout_constraintRight_toLeftOf="@+id/btn_image"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_check"
                android:layout_width="@dimen/size_footer_icon_sign"
                android:layout_height="@dimen/size_footer_icon_sign"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_sign_check" />

            <TextView
                style="@style/SignFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/str_sign_checkbox"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_check" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/background_button_tool"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_checkbox"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_image"
                android:layout_width="@dimen/size_footer_icon_sign"
                android:layout_height="@dimen/size_footer_icon_sign"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_sign_image" />

            <TextView
                style="@style/SignFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/str_sign_image"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_image" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/optionbar"
        android:layout_width="0dp"
        android:layout_height="@dimen/footer_height"
        android:background="@color/colorBackgroundFooter"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:padding="5dp"
                android:text="@string/str_select_option"
                android:textColor="@color/colorTextScanner" />

            <RelativeLayout
                android:id="@+id/rl_select_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="10dp">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    app:srcCompat="@drawable/icon_option_cancel" />
            </RelativeLayout>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <RelativeLayout
                android:id="@+id/rl_option_checkbox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:padding="5dp">

                <ImageView
                    android:id="@+id/iv_option_checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:padding="5dp"
                    app:srcCompat="@drawable/icon_option_checkbox" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_option_radio"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:padding="5dp">

                <ImageView
                    android:id="@+id/iv_option_radio"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:padding="5dp"
                    app:srcCompat="@drawable/icon_option_radio" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_option_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:padding="5dp">

                <ImageView
                    android:id="@+id/iv_option_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:padding="5dp"
                    app:srcCompat="@drawable/icon_option_cancel" />
            </RelativeLayout>
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
