<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="@color/colorBackgroundFooter">

    <LinearLayout
        android:id="@+id/btn_bar_merge"
        android:layout_width="?attr/actionBarSize"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/background_button_tool"
        android:minHeight="?attr/actionBarSize"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/btn_bar_tag"
        app:layout_constraintTop_toTopOf="@+id/btn_bar_share">

        <ImageView
            style="@style/MainFooterImage"
            android:layout_width="@dimen/size_footer_icon_main"
            android:layout_height="@dimen/size_footer_icon_main"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ic_merge_new" />

        <TextView
            style="@style/MainFooterText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/merge" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_bar_tag"
        android:layout_width="?attr/actionBarSize"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/background_button_tool"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintLeft_toRightOf="@+id/btn_bar_merge"
        app:layout_constraintRight_toLeftOf="@+id/btn_bar_share"
        app:layout_constraintTop_toTopOf="@+id/btn_bar_share">

        <ImageView
            style="@style/MainFooterImage"
            android:layout_width="@dimen/size_footer_icon_main"
            android:layout_height="@dimen/size_footer_icon_main"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ic_tag_new" />

        <TextView
            style="@style/MainFooterText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/tag" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_bar_share"
        android:layout_width="?attr/actionBarSize"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/background_button_tool"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/btn_bar_tag"
        app:layout_constraintRight_toLeftOf="@+id/btn_bar_delete"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            style="@style/MainFooterImage"
            android:layout_width="@dimen/size_footer_icon_main"
            android:layout_height="@dimen/size_footer_icon_main"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ic_share_new_color" />

        <TextView
            style="@style/MainFooterText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/ocr_share" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_bar_delete"
        android:layout_width="?attr/actionBarSize"
        android:layout_height="?attr/actionBarSize"
        android:layout_centerInParent="true"
        android:background="@drawable/background_button_tool"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@+id/btn_bar_share"
        app:layout_constraintRight_toLeftOf="@+id/btn_bar_move"
        app:layout_constraintTop_toTopOf="@+id/btn_bar_share">

        <ImageView
            style="@style/MainFooterImage"
            android:layout_width="@dimen/size_footer_icon_main"
            android:layout_height="@dimen/size_footer_icon_main"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ic_delete_new" />

        <TextView
            style="@style/MainFooterText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/str_delete" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_bar_move"
        android:layout_width="?attr/actionBarSize"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/background_button_tool"
        android:orientation="vertical"
        app:layout_constraintLeft_toRightOf="@+id/btn_bar_delete"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btn_bar_share">

        <ImageView
            style="@style/MainFooterImage"
            android:layout_width="@dimen/size_footer_icon_main"
            android:layout_height="@dimen/size_footer_icon_main"
            android:layout_gravity="center"
            app:srcCompat="@drawable/ic_main_move" />

        <TextView
            style="@style/MainFooterText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="@string/move" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
