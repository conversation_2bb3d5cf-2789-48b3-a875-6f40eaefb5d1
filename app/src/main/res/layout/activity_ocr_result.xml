<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_back_long" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/recognized_text"
                android:textColor="@color/whiteTitle"
                android:textSize="18sp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <EditText
                android:id="@+id/edit_search"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:hint="@string/search_hint"
                android:imeOptions="actionSearch"
                android:inputType="textAutoComplete"
                android:paddingRight="32dp"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textColorHint="@color/colorAccentLight"
                android:textSize="16sp"
                android:visibility="invisible"
                app:backgroundTint="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/btn_back"
                app:layout_constraintRight_toRightOf="@+id/btn_search"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/search_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="8dp"
                android:ellipsize="marquee"
                android:gravity="center"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="@+id/edit_search"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btn_search"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginRight="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_ocr_search" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_root"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@+id/footer"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp">

            <com.camscanner.docscanner.pdfcreator.features.ocr.presentation.LinedTextView
                android:id="@+id/text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:lineSpacingMultiplier="2"
                android:textColor="@color/colorTextScanner"
                android:textIsSelectable="true"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/pages_counter"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#52797979"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:paddingLeft="12dp"
        android:paddingTop="4dp"
        android:paddingRight="12dp"
        android:paddingBottom="4dp"
        android:textColor="@color/black87"
        android:textSize="20sp"
        app:layout_constraintRight_toRightOf="@+id/appbar"
        app:layout_constraintTop_toBottomOf="@+id/appbar"
        app:layout_constraintWidth_percent="0.213" />

    <com.ortiz.touchview.TouchImageView
        android:id="@+id/recognized_image"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/background_images"
        android:scaleType="matrix"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/footer"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/footer"
        android:layout_width="0dp"
        android:layout_height="@dimen/footer_height"
        android:background="@color/colorBackgroundFooter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_retake"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/btn_copy"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_retake"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                android:padding="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_ocr_retake" />

            <TextView
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/ocr_retake"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_retake" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_copy"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toRightOf="@+id/btn_retake"
            app:layout_constraintRight_toLeftOf="@+id/btn_compare"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_copy"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_ocr_copy" />

            <TextView
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/copy"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_copy" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_compare"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_copy"
            app:layout_constraintRight_toLeftOf="@+id/btn_share"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_compare"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                android:padding="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_ocr_compare" />

            <TextView
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/compare"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_compare" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_share"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_compare"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_share"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_ocr_share" />

            <TextView
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/ocr_share"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_share" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
