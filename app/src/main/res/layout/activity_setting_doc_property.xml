<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/MainAppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:popupTheme="@style/MainAppTheme.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/appbar"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@color/colorSettingBackground1">

            <TextView
                style="@style/SettingSubtitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerVertical="true"
                android:paddingLeft="10dp"
                android:paddingBottom="5dp"
                android:text="@string/setting_pdf_password_title" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/colorBGGray" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_setting_pdf_password"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true">

            <TextView
                android:id="@+id/tv_pdf_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:paddingLeft="20dp"
                android:text="@string/setting_pdf_set_password"
                android:textColor="@color/colorSettingItemColor2" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                app:srcCompat="@drawable/icon_setting_next" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/colorBGGray" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@color/colorSettingBackground1">

            <TextView
                style="@style/SettingSubtitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerVertical="true"
                android:paddingLeft="10dp"
                android:paddingBottom="5dp"
                android:text="@string/setting_pdf_direction" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/colorBGGray" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_setting_pdf_direction_portrait"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:paddingLeft="20dp"
                android:text="@string/setting_pdf_direction_portrait"
                android:textColor="@color/colorSettingItemColor2" />

            <ImageView
                android:id="@+id/iv_pdf_direction_portrait"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                app:srcCompat="@drawable/ic_check" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:layout_marginLeft="15dp"
                android:background="@color/colorBGGray" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_setting_pdf_direction_landscape"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="?attr/selectableItemBackground"
            android:clickable="true">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:paddingLeft="20dp"
                android:text="@string/setting_pdf_direction_landscape"
                android:textColor="@color/colorSettingItemColor2" />

            <ImageView
                android:id="@+id/iv_pdf_direction_ladnscape"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="5dp"
                app:srcCompat="@drawable/ic_check" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/colorBGGray" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:background="@color/colorSettingBackground1">

            <TextView
                style="@style/SettingSubtitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerVertical="true"
                android:paddingLeft="10dp"
                android:paddingBottom="5dp"
                android:text="@string/setting_pdf_size" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_alignParentBottom="true"
                android:background="@color/colorBGGray" />
        </RelativeLayout>

        <ListView
            android:id="@+id/lv_pdfsize"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="#00000000" />
    </LinearLayout>
</RelativeLayout>
