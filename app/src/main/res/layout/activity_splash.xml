<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/firstlayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="2"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">
            <ImageView
                android:id="@+id/iv_splash_icon"
                android:layout_width="130dp"
                android:layout_height="130dp"
                android:layout_gravity="center_horizontal"
                android:adjustViewBounds="true"
                android:src="@drawable/ic_splash_scanner"
                 />

            <TextView
                android:id="@+id/tv_splash_first"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Document Scanner"
                android:layout_marginTop="-10dp"
                android:textAllCaps="true"
                android:textColor="@color/colorWhite"
                android:textSize="25dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_splash_second"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/app_subtitle"
                android:textColor="@color/colorWhite"
                android:textSize="15dp" />
        </LinearLayout>




        <com.eyalbira.loadingdots.LoadingDots
            android:id="@+id/dots"
            android:layout_width="wrap_content"
            android:layout_height="10dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="40dp"
            app:LoadingDots_auto_play="true"
            app:LoadingDots_dots_color="@color/white"
            app:LoadingDots_dots_count="4"
            app:LoadingDots_dots_size="10dp"
            app:LoadingDots_dots_space="4dp"
            app:LoadingDots_jump_duration="200"
            app:LoadingDots_jump_height="4dp"
            app:LoadingDots_loop_duration="800"
            app:LoadingDots_loop_start_delay="100" />



    </RelativeLayout>
</LinearLayout>