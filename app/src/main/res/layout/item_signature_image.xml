<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:visibility="visible">

    <ImageView
        android:id="@+id/sign"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/sign_frame"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_signature_resize"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/sign"
        app:layout_constraintLeft_toLeftOf="@+id/sign"
        app:layout_constraintRight_toRightOf="@+id/sign"
        app:layout_constraintTop_toTopOf="@+id/sign" />

    <View
        android:id="@+id/sign_rotated"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/sign"
        app:layout_constraintLeft_toLeftOf="@+id/sign"
        app:layout_constraintRight_toRightOf="@+id/sign"
        app:layout_constraintTop_toTopOf="@+id/sign" />

    <View
        android:id="@+id/sign_rotated_bot_margin"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginBottom="64dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/sign_rotated"
        app:layout_constraintRight_toRightOf="@+id/sign_rotated"
        app:layout_constraintTop_toBottomOf="@+id/sign_rotated"
        app:layout_constraintVertical_bias="0" />

    <View
        android:id="@+id/sign_rotated_top_margin"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_marginTop="64dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/sign_rotated"
        app:layout_constraintLeft_toLeftOf="@+id/sign_rotated"
        app:layout_constraintRight_toRightOf="@+id/sign_rotated"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/btn_remove"
        style="@style/SimpleButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/sign_top"
        android:layout_marginLeft="4dp"
        android:paddingLeft="8dp"
        android:paddingTop="12dp"
        android:paddingRight="8dp"
        android:scaleType="fitXY"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/sign"
        app:layout_constraintDimensionRatio="W,1084:1000"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:srcCompat="@drawable/ic_sign_remove" />

    <ImageView
        android:id="@+id/btn_move"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:padding="6dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/sign"
        app:layout_constraintLeft_toLeftOf="@+id/sign"
        app:layout_constraintRight_toLeftOf="@+id/sign"
        app:layout_constraintTop_toBottomOf="@+id/sign"
        app:srcCompat="@drawable/ic_sign_move" />

    <ImageView
        android:id="@+id/btn_resize"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:padding="6dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/sign"
        app:layout_constraintLeft_toRightOf="@+id/sign"
        app:layout_constraintRight_toRightOf="@+id/sign"
        app:layout_constraintTop_toBottomOf="@+id/sign"
        app:srcCompat="@drawable/ic_sign_resize" />

    <ImageView
        android:id="@+id/btn_rotate"
        android:layout_width="@dimen/sign_ui_btn_size"
        android:layout_height="40dp"
        android:padding="6dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@+id/sign"
        app:layout_constraintLeft_toRightOf="@+id/sign"
        app:layout_constraintRight_toRightOf="@+id/sign"
        app:layout_constraintTop_toTopOf="@+id/sign"
        app:srcCompat="@drawable/ic_sign_rotate" />

    <TextView
        android:id="@+id/text_rotate"
        android:layout_width="48dp"
        android:layout_height="wrap_content"
        android:text="@string/degree"
        android:textColor="@color/colorPrimary"
        android:textSize="15sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintLeft_toRightOf="@+id/btn_rotate"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/btn_rotate" />
</androidx.constraintlayout.widget.ConstraintLayout>
