<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guid_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="48dp" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guid_vert"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.1" />

    <io.codetail.widget.RevealFrameLayout
        android:id="@+id/frame"
        android:layout_width="800dp"
        android:layout_height="750dp"
        app:layout_constraintBottom_toBottomOf="@+id/guid_horizontal"
        app:layout_constraintLeft_toLeftOf="@+id/guid_vert"
        app:layout_constraintRight_toRightOf="@+id/guid_vert"
        app:layout_constraintTop_toTopOf="@+id/guid_horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/reveal_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/background"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                android:src="@drawable/background_tutorial"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/background_small"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/background_tutorial_mini"
                app:layout_constraintBottom_toBottomOf="@+id/background_white"
                app:layout_constraintLeft_toLeftOf="@+id/background_white"
                app:layout_constraintRight_toRightOf="@+id/background_white"
                app:layout_constraintTop_toTopOf="@+id/background_white" />

            <ImageView
                android:id="@+id/background_white"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/background_tutorial_white"
                app:layout_constraintBottom_toBottomOf="@+id/btn_dot"
                app:layout_constraintLeft_toLeftOf="@+id/btn_dot"
                app:layout_constraintRight_toRightOf="@+id/btn_dot"
                app:layout_constraintTop_toTopOf="@+id/btn_dot" />

            <ImageView
                android:id="@+id/btn_dot"
                android:layout_width="@dimen/crop_dot_size"
                android:layout_height="@dimen/crop_dot_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/edge_handler" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guid_vert_inside"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.51" />

            <TextView
                android:id="@+id/message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="64dp"
                android:text="@string/tutorial_message_crop_dot"
                android:textColor="@color/white"
                android:textSize="15sp"
                app:layout_constraintBottom_toTopOf="@+id/btn_dot"
                app:layout_constraintLeft_toLeftOf="@+id/guid_vert_inside" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="@string/tutorial_title_crop_dot"
                android:textColor="@color/white"
                android:textSize="22sp"
                app:layout_constraintBottom_toTopOf="@+id/message"
                app:layout_constraintLeft_toLeftOf="@+id/message" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </io.codetail.widget.RevealFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
