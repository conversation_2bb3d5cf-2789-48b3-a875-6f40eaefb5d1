<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/list_document_height"
    android:background="@drawable/bg_doc_list"
    android:padding="@dimen/row_padding">

    <ImageView
        android:id="@+id/img_document"
        android:layout_width="107dp"
        android:layout_height="81dp"
        android:layout_marginLeft="8dp"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_doc_sample" />

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginLeft="12dp"
        android:layout_weight="5">

        <TextView
            android:id="@+id/txt_file_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:textColor="@color/colorItemText"
            android:textSize="@dimen/list_document_file_text_size"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/txt_doc_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txt_file_name"
            android:gravity="top"
            android:paddingTop="2dp"
            android:textColor="@color/colorItemText2"
            android:textSize="@dimen/list_document_doc_text_size" />

        <LinearLayout
            android:id="@+id/ll_doc_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/txt_doc_date"
            android:gravity="top"
            android:orientation="horizontal"
            android:paddingTop="3dp"
            android:visibility="invisible">

            <ImageView
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:layout_gravity="center"
                android:layout_marginRight="4dp"
                app:srcCompat="@drawable/icon_item_tag" />

            <TextView
                android:id="@+id/tv_doc_tag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:singleLine="true"
                android:textColor="@color/colorItemText2"
                android:textSize="@dimen/list_document_doc_text_size" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_detail_buttons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/img_delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:padding="4dp"
                app:srcCompat="@drawable/icon_item_delete" />

            <ImageView
                android:id="@+id/img_share"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:padding="4dp"
                app:srcCompat="@drawable/icon_item_share" />

            <ImageView
                android:id="@+id/img_bookmark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="20dp"
                android:padding="4dp"
                android:visibility="gone"
                app:srcCompat="@drawable/ic_favourite_off" />

            <ImageView
                android:id="@+id/img_doc_rename"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="4dp"
                app:srcCompat="@drawable/icon_item_edit" />
        </LinearLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/chk_selected"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginRight="10dp"
        android:visibility="gone"
        app:srcCompat="@drawable/icon_item_check_off" />
</LinearLayout>
