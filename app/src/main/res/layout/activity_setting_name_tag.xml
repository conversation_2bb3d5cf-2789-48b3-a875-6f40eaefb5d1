<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/MainAppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:popupTheme="@style/MainAppTheme.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:background="@color/colorSettingBackground1">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerVertical="true"
            android:paddingLeft="10dp"
            android:paddingBottom="5dp"
            android:text="@string/setting_name_template"
            android:textSize="10dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:background="@color/colorBGGray" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true">

        <io.github.rockerhieu.emojicon.EmojiconEditText
            android:id="@+id/emet_name_template"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="15dp"
            android:layout_toLeftOf="@+id/ivClear"
            android:background="@null"
            android:contentDescription="@string/setting_name_template"
            android:singleLine="true"
            android:text="@string/str_date"
            android:textSize="25sp"
            app:emojiconSize="40sp" />

        <ImageView
            android:id="@+id/ivClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:adjustViewBounds="true"
            android:contentDescription="@string/str_sign_clear"
            android:padding="10dp"
            app:srcCompat="@drawable/icon_close" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:background="@color/colorBGGray" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="35dp"
        android:background="@color/colorSettingBackground1">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerVertical="true"
            android:paddingLeft="10dp"
            android:paddingBottom="5dp"
            android:text="@string/setting_name_sample"
            android:textSize="10dp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:background="@color/colorBGGray" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true">

        <include
            android:id="@+id/vw_sample"
            layout="@layout/row_file" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@+id/vw_sample"
            android:background="@color/colorBGGray" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/colorSettingBackground2">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <TextView
                    android:id="@+id/tv_tag_year"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_year"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_year"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_tag_month"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_month"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_month"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_tag_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_day"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_day"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_tag_hour"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_hour"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_hour"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_tag_minute"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_minute"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_minute"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_tag_second"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_second"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_second"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_tag_tag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:background="@drawable/square_default"
                    android:contentDescription="@string/setting_name_tag_tag"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_name_tag_tag"
                    android:textColor="@color/defaultTagsTextColor"
                    android:textSize="16sp" />
            </LinearLayout>
        </HorizontalScrollView>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:background="@color/colorBGGray" />
    </RelativeLayout>
</LinearLayout>
