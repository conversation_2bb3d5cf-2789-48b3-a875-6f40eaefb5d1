<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">



    <LinearLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/colorPrimary">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center_vertical"
                android:layout_margin="10dp"
                android:clickable="true"
                android:focusable="true"
                app:srcCompat="@drawable/ic_back_long" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:text="@string/setting_enhancement_single"
                android:textColor="@color/white"
                android:textSize="18dp"></TextView>


        </LinearLayout>

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/appbar"
        android:fillViewport="false"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:background="@color/colorSettingBackground1">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_auto"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_auto"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_auto"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_original"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_original"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_original"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_lighten"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_lighten"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_lighten"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_polish"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_polish"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_polish"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_gray"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_gray"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_gray"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_bw1"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_bw1"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_bw1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_color_mode_bw2"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan_color_mode_bw2"
                    android:textColor="@color/colorSettingItemColor2" />

                <ImageView
                    android:id="@+id/iv_color_mode_bw2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/ic_check" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>
        </LinearLayout>
    </ScrollView>
</RelativeLayout>
