<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:focusable="true"
        android:focusableInTouchMode="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_back_long" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/qr_scan"
                android:textColor="@color/whiteTitle"
                android:textSize="18sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btn_list"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginRight="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_qr_history" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_root"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        android:fillViewport="true"
        android:paddingTop="16dp"
        app:layout_constraintBottom_toTopOf="@+id/footer"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginRight="18dp">

            <TextView
                android:id="@+id/text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="32dp"
                android:lineSpacingMultiplier="2"
                android:textColor="@color/colorTextScanner"
                android:textIsSelectable="true"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/footer"
        android:layout_width="0dp"
        android:layout_height="@dimen/footer_height"
        android:background="@color/colorBackgroundFooter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_copy"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/btn_share"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_copy"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_ocr_copy" />

            <TextView
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/copy"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_copy" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_share"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_copy"
            app:layout_constraintRight_toLeftOf="@+id/btn_open"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_share"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_ocr_share" />

            <TextView
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/ocr_share"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_share" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_open"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_share"
            app:layout_constraintRight_toLeftOf="@+id/btn_send"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_open"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_qr_open" />

            <TextView
                android:id="@+id/open_text"
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/open_url"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_open" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_send"
            android:layout_width="@dimen/footer_height"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/background_button_tool"
            android:gravity="center"
            android:minHeight="?attr/actionBarSize"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/btn_open"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_send"
                android:layout_width="@dimen/size_footer_icon_edit"
                android:layout_height="@dimen/size_footer_icon_edit"
                android:layout_gravity="center"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.3"
                app:srcCompat="@drawable/ic_qr_send" />

            <TextView
                android:id="@+id/send_text"
                style="@style/EditFooterText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/size_footer_text_margin"
                android:text="@string/message"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/image_send" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
