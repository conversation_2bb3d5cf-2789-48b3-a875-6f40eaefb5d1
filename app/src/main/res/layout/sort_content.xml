<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:paddingLeft="30dp" android:paddingTop="15dp" android:paddingRight="30dp" android:paddingBottom="15dp" android:layout_width="match_parent" android:layout_height="match_parent">
    <TextView android:textSize="16sp" android:textColor="@color/colorGray" android:id="@+id/tvSort" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Name" android:layout_alignParentLeft="true" android:layout_centerVertical="true"/>
    <ImageView android:id="@+id/ivSort" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentRight="true" android:layout_centerVertical="true"/>
</RelativeLayout>
