<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:layout_width="match_parent" android:layout_height="match_parent">
    <com.google.android.material.appbar.AppBarLayout android:theme="@style/MainAppTheme.AppBarOverlay" android:id="@+id/appbar" android:layout_width="match_parent" android:layout_height="wrap_content">
        <androidx.appcompat.widget.Toolbar android:id="@+id/toolbar" android:background="@color/colorPrimary" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" app:popupTheme="@style/MainAppTheme.PopupOverlay"/>
    </com.google.android.material.appbar.AppBarLayout>
    <RelativeLayout android:id="@+id/rl_topview" android:background="@color/colorSettingBackground1" android:layout_width="match_parent" android:layout_height="30dp" android:layout_below="@+id/appbar">
        <View android:background="@color/colorBGGray" android:layout_width="match_parent" android:layout_height="1dp" android:layout_alignParentBottom="true"/>
    </RelativeLayout>
    <ListView android:id="@+id/lv_pdfsize" android:layout_width="match_parent" android:layout_height="match_parent" android:divider="#00000000" android:layout_below="@+id/rl_topview"/>
</RelativeLayout>
