<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <View
        android:id="@+id/guid_horizontal"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginBottom="60dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <io.codetail.widget.RevealFrameLayout
        android:id="@+id/frame"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/guid_horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guid_horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/reveal_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/background"
                android:layout_width="550dp"
                android:layout_height="555dp"
                android:scaleType="fitXY"
                android:src="@drawable/background_tutorial"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/background_small"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/background_tutorial_mini"
                app:layout_constraintBottom_toBottomOf="@+id/btn_camera"
                app:layout_constraintLeft_toLeftOf="@+id/btn_camera"
                app:layout_constraintRight_toRightOf="@+id/btn_camera"
                app:layout_constraintTop_toTopOf="@+id/btn_camera" />

            <ImageView
                android:id="@+id/btn_camera"
                android:layout_width="@dimen/fab_big"
                android:layout_height="@dimen/fab_big"
                android:background="@drawable/bg_floating_button"
                android:clickable="true"
                android:focusable="true"
                android:padding="16dp"
                app:layout_constraintBottom_toBottomOf="@+id/background"
                app:layout_constraintLeft_toLeftOf="@+id/background"
                app:layout_constraintRight_toRightOf="@+id/background"
                app:layout_constraintTop_toTopOf="@+id/background"
                app:srcCompat="@drawable/ic_camera" />

            <TextView
                android:id="@+id/message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginBottom="48dp"
                android:text="@string/tutorial_message_camera"
                android:textColor="@color/white"
                android:textSize="15sp"
                app:layout_constraintBottom_toTopOf="@+id/btn_camera"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginBottom="4dp"
                android:text="@string/tutorial_title_camera"
                android:textColor="@color/white"
                android:textSize="22sp"
                app:layout_constraintBottom_toTopOf="@+id/message"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </io.codetail.widget.RevealFrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
