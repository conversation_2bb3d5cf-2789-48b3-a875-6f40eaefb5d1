<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/MainAppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:popupTheme="@style/MainAppTheme.PopupOverlay">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/btn_back"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:contentDescription="@string/back"
                    android:padding="8dp"
                    app:srcCompat="@drawable/ic_back_long" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingLeft="4dp"
                    android:paddingTop="4dp"
                    android:paddingRight="4dp"
                    android:paddingBottom="4dp"
                    android:text="@string/setting_title"
                    android:textColor="@color/colorWhite"
                    android:textSize="18sp" />
            </LinearLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/appbar"
        android:fillViewport="false"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="55dp"
                android:background="@color/colorSettingBackground1">

                <TextView
                    style="@style/SettingSubtitleText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:paddingBottom="5dp"
                    android:text="@string/setting_general" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_setting_scan"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_scan"
                    android:textColor="@color/colorSettingItemColor2"
                    android:textSize="14sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/icon_setting_next" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rl_setting_display"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="?attr/selectableItemBackground"
                android:clickable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="20dp"
                    android:text="@string/setting_display_pdf"
                    android:textColor="@color/colorSettingItemColor2"
                    android:textSize="14sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="5dp"
                    app:srcCompat="@drawable/icon_setting_next" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="15dp"
                    android:background="@color/colorBGGray" />
            </RelativeLayout>



<!--            <RelativeLayout-->
<!--                android:id="@+id/rl_setting_language"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="50dp"-->
<!--                android:background="?attr/selectableItemBackground"-->
<!--                android:clickable="true">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:paddingLeft="20dp"-->
<!--                    android:text="@string/setting_language"-->
<!--                    android:textColor="@color/colorSettingItemColor2"-->
<!--                    android:textSize="14sp" />-->

<!--                <ImageView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="5dp"-->
<!--                    app:srcCompat="@drawable/icon_setting_next" />-->

<!--                <View-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="1dp"-->
<!--                    android:layout_alignParentBottom="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:background="@color/colorBGGray" />-->
<!--            </RelativeLayout>-->

<!--            <RelativeLayout-->
<!--                android:id="@+id/rl_setting_privacy"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="50dp"-->
<!--                android:background="?attr/selectableItemBackground"-->
<!--                android:clickable="true">-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:paddingLeft="20dp"-->
<!--                    android:text="@string/setting_privacy"-->
<!--                    android:textColor="@color/colorSettingItemColor2"-->
<!--                    android:textSize="14sp" />-->

<!--                <ImageView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_alignParentRight="true"-->
<!--                    android:layout_centerVertical="true"-->
<!--                    android:layout_marginRight="5dp"-->
<!--                    app:srcCompat="@drawable/icon_setting_next" />-->

<!--                <View-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="1dp"-->
<!--                    android:layout_alignParentBottom="true"-->
<!--                    android:layout_marginLeft="15dp"-->
<!--                    android:background="@color/colorBGGray" />-->
<!--            </RelativeLayout>-->
        </LinearLayout>
    </ScrollView>
</RelativeLayout>
