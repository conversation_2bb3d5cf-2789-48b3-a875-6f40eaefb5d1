<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/dialog"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginRight="4dp"
        app:cardBackgroundColor="@color/appbid_white"
        app:cardCornerRadius="8dp"
        app:cardUseCompatPadding="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/logo"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginLeft="@dimen/appbid_margin_side_title"
                android:layout_marginTop="12dp"
                app:layout_constraintEnd_toStartOf="@+id/title"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="@dimen/appbid_margin_side_title"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:textColor="@color/appbid_black_text"
                android:textSize="18sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="@+id/logo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/logo"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toEndOf="@+id/logo"
                app:layout_constraintTop_toTopOf="@+id/logo" />

            <TextView
                android:id="@+id/text_question"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="8dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:gravity="center"
                android:text="@string/appsee_question_line_text"
                android:textColor="@color/appbid_black_text"
                android:textSize="18sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/logo" />

            <TextView
                android:id="@+id/text_second"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="12dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:textColor="@color/appbid_gray_text"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/text_question" />

            <TextView
                android:id="@+id/first_button"
                style="@style/Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="16dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:text="@string/appsee_ok_btn"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/text_second" />

            <TextView
                android:id="@+id/second_button"
                style="@style/Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="8dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:text="@string/appsee_no_btn"
                app:layout_constraintBottom_toTopOf="@+id/third_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/first_button"
                app:layout_goneMarginBottom="12dp" />

            <TextView
                android:id="@+id/third_button"
                style="@style/Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/appbid_margin_side_text"
                android:layout_marginTop="8dp"
                android:layout_marginRight="@dimen/appbid_margin_side_text"
                android:layout_marginBottom="12dp"
                android:text="@string/appsee_pay_btn"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/second_button" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>
