<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/MainAppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/MainAppTheme.PopupOverlay">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_sign_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:background="?attr/selectableItemBackground"
                    android:contentDescription="@string/str_sign_cancel"
                    android:padding="10dp"
                    android:text="@string/str_sign_cancel"
                    android:textColor="@color/colorWhite" />

                <TextView
                    android:id="@+id/tv_sign_adopt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="20dp"
                    android:background="?attr/selectableItemBackground"
                    android:contentDescription="@string/str_sign_adopt"
                    android:padding="10dp"
                    android:text="@string/str_sign_adopt"
                    android:textColor="@color/colorWhite" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/str_sign_title"
                    android:textAllCaps="true"
                    android:textColor="@color/colorWhite"
                    android:textStyle="bold" />
            </RelativeLayout>
        </androidx.appcompat.widget.Toolbar>
    </com.google.android.material.appbar.AppBarLayout>

    <com.github.gcacace.signaturepad.views.SignaturePad
        android:id="@+id/sp_pad"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/vw_seperator"
        android:layout_below="@+id/appbar" />

    <ImageView
        android:id="@+id/iv_signature"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/vw_seperator"
        android:layout_below="@+id/appbar" />

    <View
        android:id="@+id/vw_seperator"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:layout_above="@+id/rl_sign_control"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/bg_dashline" />

    <RelativeLayout
        android:id="@+id/rl_sign_control"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rl_sign_pick"
        android:layout_marginLeft="30dp"
        android:layout_marginRight="30dp">

        <TextView
            android:id="@+id/tv_sign_clear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="10dp"
            android:text="@string/str_sign_clear"
            android:textColor="@color/colorAccent" />

        <RelativeLayout
            android:id="@+id/rl_sign_color_black"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="5dp"
            android:layout_toLeftOf="@+id/rl_sign_color_blue">

            <ImageView
                android:id="@+id/iv_sign_outline_black"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/bg_sign_color_outline" />

            <ImageView
                android:id="@+id/iv_sign_color_black"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:padding="2dp"
                android:src="@drawable/bg_sign_color_black" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_sign_color_blue"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="5dp"
            android:layout_toLeftOf="@+id/rl_sign_color_red">

            <ImageView
                android:id="@+id/iv_sign_outline_blue"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/bg_sign_color_outline" />

            <ImageView
                android:id="@+id/iv_sign_color_blue"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:padding="2dp"
                android:src="@drawable/bg_sign_color_blue" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_sign_color_red"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true">

            <ImageView
                android:id="@+id/iv_sign_outline_red"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:src="@drawable/bg_sign_color_outline" />

            <ImageView
                android:id="@+id/iv_sign_color_red"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_centerInParent="true"
                android:adjustViewBounds="true"
                android:padding="2dp"
                android:src="@drawable/bg_sign_color_red" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rl_sign_pick"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:padding="10dp">

        <TextView
            android:id="@+id/tv_sign_pick"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:contentDescription="@string/str_sign_pick"
            android:padding="10dp"
            android:text="@string/str_sign_pick"
            android:textAllCaps="true"
            android:textColor="@color/colorAccent" />
    </RelativeLayout>
</RelativeLayout>
