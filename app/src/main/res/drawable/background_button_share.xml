<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/white"/>
            <stroke android:width="1dp" android:color="@color/colorPrimary"/>
            <corners android:radius="8dp"/>
        </shape>
    </item>
    <item android:state_pressed="true">
        <color android:color="@color/colorWhitePressed"/>
    </item>
    <item android:drawable="@color/colorWhitePressed"/>
</selector>
