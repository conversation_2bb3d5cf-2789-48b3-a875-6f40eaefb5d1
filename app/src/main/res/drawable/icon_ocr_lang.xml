<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="400"
    android:viewportHeight="400">
    <path
        android:fillColor="#00000000"
        android:pathData="M220,229m-54,0a54,54 0,1 1,108 0a54,54 0,1 1,-108 0"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M310.86,356.69c-1.6,1.32 -4,1.31 -6.19,1.31H93.33A15.38,15.38 0,0 1,78 342.67V62.33A15.38,15.38 0,0 1,93.33 47H244.67"
        android:strokeWidth="7"
        android:strokeColor="#0A1526"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M320,121.33V342.67c0,2.12 0.12,4.89 -1.22,6"
        android:strokeWidth="7"
        android:strokeColor="#0A1526"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M320,122 L244,47v63a12,12 0,0 0,12 12Z"
        android:strokeWidth="7"
        android:strokeColor="#0A1526"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M114,125L226,125"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M129,159L275,159"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M114,194L178,194"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M129,228L165,228"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M178,263L114,263"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M129,293L245,293"
        android:strokeWidth="4"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00000000"
        android:pathData="M253.5,270.5l75.89,90.2a5.37,5.37 0,0 1,-0.85 7.71h0a5.37,5.37 0,0 1,-7.34 -0.78l-9.61,-11.3L244.5,277.5"
        android:strokeWidth="3"
        android:strokeColor="#4e656f"
        android:strokeLineCap="round"
        android:strokeLineJoin="round" />
    <path
        android:fillColor="#00bfa5"
        android:pathData="M230,257.87a3,3 0,0 1,-1.36 -0.3,2.84 2.84,0 0,1 -1,-0.78 3.43,3.43 0,0 1,-0.59 -1.12,4.25 4.25,0 0,1 -0.2,-1.26 6,6 0,0 1,0.29 -1.55c0.2,-0.65 0.45,-1.39 0.75,-2.21s0.64,-1.67 1,-2.57 0.72,-1.75 1.06,-2.56 0.63,-1.58 0.87,-2.27 0.42,-1.24 0.52,-1.64 0.22,-1 0.36,-1.6 0.31,-1.29 0.5,-2 0.36,-1.5 0.54,-2.3l0.57,-2.44c-0.34,0.08 -0.76,0.14 -1.26,0.21l-1.59,0.16c-0.81,0.08 -1.6,0.19 -2.38,0.34s-1.58,0.31 -2.39,0.5 -1.66,0.38 -2.54,0.6l-2.82,0.65q-2.19,0.48 -4.38,1.08a17.73,17.73 0,0 1,-4.57 0.59A2.53,2.53 0,0 1,210 237a4.69,4.69 0,0 0,-1.39 -0.58c-0.48,0.8 -0.95,1.65 -1.39,2.55s-0.84,1.84 -1.2,2.79 -0.7,1.9 -1,2.83 -0.55,1.8 -0.77,2.62a13.52,13.52 0,0 0,-0.31 1.73c-0.08,0.71 -0.16,1.43 -0.23,2.16s-0.15,1.43 -0.22,2.1 -0.15,1.2 -0.22,1.6a3.52,3.52 0,0 1,-0.44 1.1,4.69 4.69,0 0,1 -0.81,1 4.09,4.09 0,0 1,-1.06 0.73,2.79 2.79,0 0,1 -1.17,0.28 2.3,2.3 0,0 1,-1.66 -0.62,4.42 4.42,0 0,1 -1,-1.52 7.37,7.37 0,0 1,-0.53 -2,14.46 14.46,0 0,1 -0.15,-2 21.12,21.12 0,0 1,0.55 -4.71,38.4 38.4,0 0,1 1.48,-4.95 53.36,53.36 0,0 1,2.19 -5.06c0.84,-1.69 1.75,-3.39 2.73,-5.07 0.8,-1.52 1.8,-3.26 3,-5.25s2.53,-4 4,-6.12 3.07,-4.14 4.79,-6.15a47.73,47.73 0,0 1,5.35 -5.38,28.35 28.35,0 0,1 5.7,-3.81 13,13 0,0 1,5.82 -1.45,10.32 10.32,0 0,1 5.06,1.12 8.77,8.77 0,0 1,3.24 3,12.75 12.75,0 0,1 1.7,4.43 28.6,28.6 0,0 1,0.49 5.35,45.5 45.5,0 0,1 -0.31,5.13c-0.21,1.77 -0.47,3.49 -0.77,5.17s-0.63,3.31 -1,4.88 -0.67,3 -1,4.24l-1.28,5.06c0,0.14 -0.14,0.44 -0.28,0.89s-0.3,1 -0.51,1.62 -0.44,1.31 -0.7,2 -0.51,1.45 -0.76,2.16 -0.5,1.36 -0.73,2 -0.44,1.08 -0.61,1.45l0,0c-0.17,0.64 -0.35,1.3 -0.55,2a6.7,6.7 0,0 1,-0.8 1.8,4.1 4.1,0 0,1 -1.32,1.3A3.84,3.84 0,0 1,230 257.87ZM231.86,211.3a8.44,8.44 0,0 0,-3.27 0.68,16.6 16.6,0 0,0 -3.2,1.81 23.39,23.39 0,0 0,-3 2.6c-1,1 -1.93,2 -2.82,3.05s-1.72,2.09 -2.5,3.14 -1.49,2 -2.12,2.92l-0.39,0.58c-0.15,0.24 -0.31,0.5 -0.47,0.78s-0.31,0.59 -0.46,0.9 -0.27,0.58 -0.36,0.83h3.76a41.69,41.69 0,0 0,4.68 -0.3c1.76,-0.19 3.49,-0.43 5.21,-0.7s3.3,-0.55 4.74,-0.84l3.25,-0.65a9,9 0,0 0,0.27 -1.3c0.09,-0.55 0.17,-1.15 0.24,-1.82s0.13,-1.39 0.17,-2.14 0.05,-1.53 0.05,-2.33c0,-0.64 0,-1.38 -0.07,-2.21a9.49,9.49 0,0 0,-0.44 -2.36,4.79 4.79,0 0,0 -1.12,-1.88A2.8,2.8 0,0 0,231.82 211.3Z" />
</vector>
