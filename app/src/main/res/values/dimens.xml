<?xml version="1.0" encoding="utf-8"?>
<resources>

    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="appbid_flow_size">300dp</dimen>
    <dimen name="appbid_margin_side_text">12dp</dimen>
    <dimen name="appbid_margin_side_title">16dp</dimen>
    <dimen name="border_camera_button">2dp</dimen>
    <dimen name="crop_dot_radius">14dp</dimen>
    <dimen name="crop_dot_size">28dp</dimen>
    <dimen name="defaultTagsCloseImagePadding">10dp</dimen>
    <dimen name="defaultTagsPadding">0dp</dimen>
    <dimen name="defaultTagsTextSize">16sp</dimen>
    <dimen name="delete_area_height">96dp</dimen>
    <dimen name="fab_big">80dp</dimen>
    <dimen name="fab_margin">20dp</dimen>
    <dimen name="fab_medium">64dp</dimen>
    <dimen name="fab_small">56dp</dimen>
    <dimen name="filter_item_distance">6dp</dimen>
    <dimen name="filter_item_padding">2dp</dimen>
    <dimen name="filter_toolbar_height">80dp</dimen>
    <dimen name="footer_height">84dp</dimen>
    <dimen name="footer_size">72dp</dimen>
    <dimen name="footer_size_camera">84dp</dimen>
    <dimen name="footer_toolbar_height">90dp</dimen>
    <dimen name="init_size_font">22sp</dimen>
    <dimen name="init_size_mark">50dp</dimen>
    <dimen name="init_size_sign">100dp</dimen>
    <dimen name="list_document_doc_text_size">10sp</dimen>
    <dimen name="list_document_file_text_size">14sp</dimen>
    <dimen name="list_document_folder_height">50dp</dimen>
    <dimen name="list_document_height">100dp</dimen>
    <dimen name="margin_mag_distance">32dp</dimen>
    <dimen name="margin_mag_side">90dp</dimen>
    <dimen name="margin_side_bottom_btn">14dp</dimen>
    <dimen name="margin_side_top_btn">14dp</dimen>
    <dimen name="margin_top_top_btn">16dp</dimen>
    <dimen name="min_diff_centers">4dp</dimen>
    <dimen name="min_side_tutorial_margin">24dp</dimen>
    <dimen name="min_sign_size">32dp</dimen>
    <dimen name="min_top_tutorial_margin">56dp</dimen>
    <dimen name="ocr_text_divider_height">1dp</dimen>
    <dimen name="padding_grid">5dp</dimen>
    <dimen name="handal_size">7dp</dimen>
    <dimen name="padding_search_word">32dp</dimen>
    <dimen name="preview_image_size">120dp</dimen>
    <dimen name="qr_border_length">32dp</dimen>
    <dimen name="qr_border_width">5dp</dimen>
    <dimen name="qr_frame_width">1dp</dimen>
    <dimen name="rateuslib_circle_width">4dp</dimen>
    <dimen name="rateuslib_dialog_margin_side">32dp</dimen>
    <dimen name="rateuslib_difference_between_screens">125dp</dimen>
    <dimen name="rateuslib_google_bottom">8dp</dimen>
    <dimen name="rateuslib_google_side">24dp</dimen>
    <dimen name="rateuslib_icon_size">48dp</dimen>
    <dimen name="rateuslib_padding_text">12dp</dimen>
    <dimen name="rateuslib_scene_big_size_excellent">400dp</dimen>
    <dimen name="rateuslib_scene_big_size_good">250dp</dimen>
    <dimen name="rateuslib_scene_normal_size">200dp</dimen>
    <dimen name="rateuslib_star">30dp</dimen>
    <dimen name="rateuslib_star_margin">4dp</dimen>
    <dimen name="resize_interval">1dp</dimen>
    <dimen name="row_padding">10dp</dimen>
    <dimen name="shared_text_size">15sp</dimen>
    <dimen name="shift_centers">8dp</dimen>
    <dimen name="sign_text_max">80sp</dimen>
    <dimen name="sign_text_min">5sp</dimen>
    <dimen name="sign_text_padding">6dp</dimen>
    <dimen name="sign_text_step">1sp</dimen>
    <dimen name="sign_top">48dp</dimen>
    <dimen name="sign_ui_btn_size">40dp</dimen>
    <dimen name="size_footer_icon_edit">52dp</dimen>
    <dimen name="size_footer_icon_main">26dp</dimen>
    <dimen name="size_footer_icon_sign">52dp</dimen>
    <dimen name="size_footer_text_margin">48dp</dimen>
    <dimen name="size_tool_image">32dp</dimen>
    <dimen name="size_tool_image_crop">26dp</dimen>
    <dimen name="squarecamera_cover_start_height">300dp</dimen>
    <dimen name="texts_tutorial_margin">4dp</dimen>
    <dimen name="thump_stepslider_shadow">1dp</dimen>
    <dimen name="wakeads_stroke_width">1dp</dimen>
    <dimen name="width_tool_btn">56dp</dimen>
</resources>
