package com.jakewharton.rxbinding2.widget;

import androidx.annotation.NonNull;
import android.widget.SeekBar;

final class AutoValue_SeekBarProgressChangeEvent extends SeekBarProgressChangeEvent {
    private final boolean fromUser;
    private final int progress;
    private final SeekBar view;

    AutoValue_SeekBarProgressChangeEvent(SeekBar view2, int progress2, boolean fromUser2) {
        if (view2 != null) {
            this.view = view2;
            this.progress = progress2;
            this.fromUser = fromUser2;
            return;
        }
        throw new NullPointerException("Null view");
    }

    @NonNull
    public SeekBar view() {
        return this.view;
    }

    public int progress() {
        return this.progress;
    }

    public boolean fromUser() {
        return this.fromUser;
    }

    public String toString() {
        return "SeekBarProgressChangeEvent{view=" + this.view + ", progress=" + this.progress + ", fromUser=" + this.fromUser + "}";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof SeekBarProgressChangeEvent)) {
            return false;
        }
        SeekBarProgressChangeEvent that = (SeekBarProgressChangeEvent) o;
        if (this.view.equals(that.view()) && this.progress == that.progress() && this.fromUser == that.fromUser()) {
            return true;
        }
        return false;
    }

    public int hashCode() {
        return (((((1 * 1000003) ^ this.view.hashCode()) * 1000003) ^ this.progress) * 1000003) ^ (this.fromUser ? 1231 : 1237);
    }
}
