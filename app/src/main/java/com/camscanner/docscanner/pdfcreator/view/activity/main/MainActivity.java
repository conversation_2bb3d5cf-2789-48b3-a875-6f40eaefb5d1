package com.camscanner.docscanner.pdfcreator.view.activity.main;

import android.content.ActivityNotFoundException;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import android.os.Environment;
import android.provider.Settings;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.FragmentManager;

import com.camscanner.docscanner.pdfcreator.BuildConfig;
import com.camscanner.docscanner.pdfcreator.common.utils.PermissionsUtils;
import com.camscanner.docscanner.pdfcreator.view.activity.FolderMainActivity;
import com.google.android.material.navigation.NavigationView;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.ru.admaster.Privacy_Policy;
import com.tapscanner.polygondetect.PDFBoxResourceLoader;

import com.camscanner.docscanner.pdfcreator.Glob;
import com.camscanner.docscanner.pdfcreator.R;
import com.camscanner.docscanner.pdfcreator.common.Constant;
import com.camscanner.docscanner.pdfcreator.common.utils.ImageStorageUtils;
import com.camscanner.docscanner.pdfcreator.features.premium.PremiumHelper;
import com.camscanner.docscanner.pdfcreator.features.tutorial.TutorialManager;
import com.camscanner.docscanner.pdfcreator.features.tutorial.model.TutorialInfo;
import com.camscanner.docscanner.pdfcreator.view.activity.BaseMainActivity;
import com.camscanner.docscanner.pdfcreator.view.activity.GridModeLayoutActivity;
import com.camscanner.docscanner.pdfcreator.view.activity.setting.Setting_MyActivity;
import com.camscanner.docscanner.pdfcreator.view.activity.signature.SignPadActivity;
import com.camscanner.docscanner.pdfcreator.view.fragment.MainFragment;

public class MainActivity extends BaseMainActivity implements NavigationView.OnNavigationItemSelectedListener,  TutorialManager.OnTutorialListener {
    private static final String strDocFragTitle = "frag_doc";
    private boolean afterNewDoc;
    private boolean afterReceived;
    private ImageView btnMenu;
    private DrawerLayout drawer;
    private String lastName;
    private String lastParent;
    private FirebaseAnalytics mFirebaseAnalytics;
//    private MenuItem m_navAdsRemove;
//    private MenuItem m_navUpgradePremium;


    private MainFragment mainFragment = null;
    private NavigationView navigationView;

    public enum ACTIVE_VIEW {
        DOCS,
        SHARED,
        TAGS
    }


    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView((int) R.layout.activity_main);
        if (Build.VERSION.SDK_INT >= 30) {
            if (!Environment.isExternalStorageManager()) {
                Intent permissionIntent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivity(permissionIntent);
            }
        }


        mFirebaseAnalytics = FirebaseAnalytics.getInstance(this);

        Intent intent = getIntent();


        initUI();
        if (savedInstanceState == null) {
            replaceFragment(ACTIVE_VIEW.DOCS);
        }

            this.afterReceived = true;

        PDFBoxResourceLoader.init(getApplicationContext());
    }

    /* access modifiers changed from: protected */
    public void onStart() {
        super.onStart();
//        Analytics.get().logDocsScreen();
    }



    /* access modifiers changed from: protected */
    public void onResume() {
        super.onResume();


    }

    /* access modifiers changed from: package-private */
    public void initUI() {
        setSupportActionBar((Toolbar) findViewById(R.id.toolbar));
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayShowTitleEnabled(false);
        }
        this.drawer = (DrawerLayout) findViewById(R.id.drawer_layout);
        this.btnMenu = (ImageView) findViewById(R.id.btn_menu);
        this.btnMenu.setOnClickListener(new View.OnClickListener() {
            public final void onClick(View view) {
                MainActivity.this.lambda$initUI$0$MainActivity(view);
            }
        });
        this.navigationView = (NavigationView) findViewById(R.id.nav_view);
        this.navigationView.setNavigationItemSelectedListener(this);
        this.navigationView.setItemIconTintList((ColorStateList) null);

//        this.m_swtHDQuality = (SwitchCompat) this.navigationView.getMenu().findItem(R.id.nav_hd_quality).getActionView().findViewById(R.id.drawer_switch);
//        this.m_swtHDQuality.setOnClickListener(new View.OnClickListener() {
//            public final void onClick(View view) {
//                MainActivity.this.lambda$initUI$1$MainActivity(view);
//            }
//        });
//        if (ScanApplication.userRepo().isUserPremium(this)) {
//            this.m_swtHDQuality.setChecked(true);
//        } else {
//            this.m_swtHDQuality.setChecked(false);
//        }
    }

    public /* synthetic */ void lambda$initUI$0$MainActivity(View v) {
        if (this.drawer.isDrawerOpen((int) GravityCompat.START)) {
            this.drawer.closeDrawer((int) GravityCompat.START);
        } else {
            this.drawer.openDrawer((int) GravityCompat.START);
        }
    }

    public /* synthetic */ void lambda$initUI$1$MainActivity(View v) {

//            Analytics.get().logPremiumFeature(AnalyticsConstants.PARAM_VALUE_PREMIUM_FEATURE_HD_QUALITY);
            PremiumHelper.showPremiumAfterAlertDialog(this, R.string.premium_feature_hd_quality, R.string.alert_premium_hd_message, new PremiumHelper.StartActivityController() {
                public final void startActivity(Intent intent, int i) {
                    MainActivity.this.startActivityForResult(intent, i);
                }
            });
//            this.m_swtHDQuality.setChecked(false);

    }

    public void onSaveInstanceState(Bundle savedInstanceState) {
        savedInstanceState.putBoolean("created", true);
        super.onSaveInstanceState(savedInstanceState);
    }

    public boolean onCreateOptionsMenu(Menu menu) {

        return true;
    }



    public boolean onOptionsItemSelected(MenuItem item) {
        return super.onOptionsItemSelected(item);
    }

    public void onBackPressed() {
        DrawerLayout drawer2 = (DrawerLayout) findViewById(R.id.drawer_layout);
        if (drawer2.isDrawerOpen((int) GravityCompat.START)) {
            drawer2.closeDrawer((int) GravityCompat.START);
            return;
        }
//        MainFragment mainFragment2 = this.mainFragment;
//        if (mainFragment2 == null || !mainFragment2.isVisible() || !this.mainFragment.isMultiMode()) {
//            finish();
//        } else {
//            this.mainFragment.setMultiMode(false);
//        }


        AlertDialog.Builder dialog = new AlertDialog.Builder(MainActivity.this);
        dialog.setMessage("Do you want to exit?")
//     .setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
//      public void onClick(DialogInterface dialoginterface, int i) {
//          dialoginterface.cancel();
//          }})
                .setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialoginterface, int i) {
                        finishAffinity();
                    }
                }).setNegativeButton("No", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialogInterface, int i) {
                dialogInterface.dismiss();

            }
        }).show();

    }




    public boolean onNavigationItemSelected(MenuItem item) {
        int id = item.getItemId();
        switch (id) {



            case R.id.nav_rate_us:
                rate();
                break;

            case R.id.nav_setting:
                goToSetting();
                break;
            case R.id.nav_share:
                try {
                    Intent shareIntent = new Intent(Intent.ACTION_SEND);
                    shareIntent.setType("text/plain");
                    shareIntent.putExtra(Intent.EXTRA_SUBJECT, "My application name");
                    String shareMessage= "\nLet me recommend you this application\n\n";
                    shareMessage = shareMessage + "https://play.google.com/store/apps/details?id=" + BuildConfig.APPLICATION_ID +"\n\n";
                    shareIntent.putExtra(Intent.EXTRA_TEXT, shareMessage);
                    startActivity(Intent.createChooser(shareIntent, "choose one"));
                } catch(Exception e) {
                    //e.toString();
                }
                break;
            case R.id.nav_privacy:

                Privacy_Policy.Privacy(MainActivity.this);
                break;
//            case R.id.nav_signature:
//                goToSignPad();
//                break;

        }
        DrawerLayout drawer2 = (DrawerLayout) findViewById(R.id.drawer_layout);

        return true;
    }

    private void rate() {
        if (Glob.isOnline( MainActivity.this )) {
            try {
                Uri uri = Uri.parse( "market://details?id=" + getPackageName() );
                Intent myAppLinkToMarket = new Intent( Intent.ACTION_VIEW, uri );
                try {
                    startActivity( myAppLinkToMarket );
                } catch (ActivityNotFoundException e) {
                    Toast.makeText( getApplicationContext(), "Unable to find market app", Toast.LENGTH_SHORT ).show();
                }
            } catch (Exception e) {
            }
        } else {
            Toast.makeText( getApplicationContext(), "No Internet Connection Available", Toast.LENGTH_SHORT ).show();
        }
    }



    public void goToSetting() {
        startActivity(new Intent(this, Setting_MyActivity.class));
    }

    

    private void goToSignPad() {
        startActivityForResult(new Intent(this, SignPadActivity.class), 1014);
    }



    /* access modifiers changed from: package-private */
    public void replaceFragment(ACTIVE_VIEW view) {
        FragmentManager fragmentManager = getSupportFragmentManager();
        if (C68591.f14690xf0e6397e[view.ordinal()] == 1) {
            this.mainFragment = new MainFragment();
            fragmentManager.beginTransaction().replace(R.id.frameContainer, this.mainFragment, strDocFragTitle).addToBackStack("").commit();
        }
    }


    static /* synthetic */ class C68591 {

        static final /* synthetic */ int[] f14690xf0e6397e = new int[ACTIVE_VIEW.values().length];

        static {
            try {
                f14690xf0e6397e[ACTIVE_VIEW.DOCS.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
        }
    }

    /* access modifiers changed from: protected */
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        MainFragment mainFragment2 = this.mainFragment;
        if (mainFragment2 != null) {
            mainFragment2.m_bActivityChanged = true;
        }
        if (requestCode != 1002) {
            if (requestCode != 1010) {
                super.onActivityResult(requestCode, resultCode, data);
                return;
            }
            ImageStorageUtils.clearShareFolder();
        } else if (resultCode == -1) {
            String mParent = data.getExtras().getString(Constant.EXTRA_MPARENT);
            String mName = data.getExtras().getString(Constant.EXTRA_MNAME);

            autoOpenFile(mParent, mName);
//            if (!ScanApplication.adsManager.show(false)) {
//                autoOpenFile(mParent, mName);
//                return;
//            }
            this.afterNewDoc = true;
            this.lastParent = mParent;
            this.lastName = mName;
        }
    }

    public void autoOpenFile(String mParent, String mName) {
        MainFragment mainFragment2 = this.mainFragment;
        if (mainFragment2 != null) {
            mainFragment2.autoOpenFile(mParent, mName);
            return;
        }
        Intent intent = new Intent(this, GridModeLayoutActivity.class);
        Constant.m_strParent= mParent;
        intent.putExtra(Constant.EXTRA_PARENT, mParent);
        intent.putExtra("name", mName);
        startActivityForResult(intent, 1006);
    }







    public void openFile1(String mParent, String mName) {
        Intent intent = new Intent(this, GridModeLayoutActivity.class);
        Constant.m_strParent= mParent;
        intent.putExtra(Constant.EXTRA_PARENT, mParent);
        intent.putExtra("name", mName);
        startActivityForResult(intent, 1006);
    }




    public void onTutorialViewClicked(View v) {
        MainFragment mainFragment2 = this.mainFragment;
        if (mainFragment2 != null) {
            mainFragment2.onTutorialViewClicked(v);
        }
    }

    public void onTutorialClosed(TutorialInfo tutorialInfo, boolean targetHit) {
        MainFragment mainFragment2 = this.mainFragment;
        if (mainFragment2 != null) {
            mainFragment2.onTutorialClosed(tutorialInfo, targetHit);
        }
//        Analytics.get().logTutorCamera(targetHit);
    }
}
