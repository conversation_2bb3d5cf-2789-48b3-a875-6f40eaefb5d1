package com.camscanner.docscanner.pdfcreator;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;

import androidx.appcompat.app.AppCompatDelegate;
import androidx.multidex.MultiDexApplication;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.onesignal.OneSignal;
import com.ru.admaster.Get_All_ID;
import com.ru.admaster.Google_Intertitial;
import com.ru.admaster.Utils;
import com.tapscanner.polygondetect.PolygonDetectEngineInterface;
import org.opencv.android.BaseLoaderCallback;
import org.opencv.android.OpenCVLoader;

import com.camscanner.docscanner.pdfcreator.common.p018db.DBManager;
import com.camscanner.docscanner.pdfcreator.common.utils.ImageStorageUtils;
import com.camscanner.docscanner.pdfcreator.common.utils.LanguageUtil.LocalManageUtil;
import com.camscanner.docscanner.pdfcreator.common.utils.ProcessUtils;
import com.camscanner.docscanner.pdfcreator.features.picture.ImageHolder;
import com.camscanner.docscanner.pdfcreator.features.subscription.SubscriptionManager;
import com.camscanner.docscanner.pdfcreator.features.subscription.UserRepository;
import com.camscanner.docscanner.pdfcreator.view.activity.login.SplashActivity;
import timber.log.Timber;

public class ScanApplication extends Application implements Application.ActivityLifecycleCallbacks {
    private static final String LOG_TAG = "ScanApplication";

    private static PolygonDetectEngineInterface gDetectionEngine;
    private static ImageHolder imageHolder;
    private static SubscriptionManager subManager;
    private static UserRepository userRepo;
    private BaseLoaderCallback mLoaderCallback;

    private void initDebug() {
    }



    public void onActivityDestroyed(Activity activity) {
    }

    public void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
    }

    public void onConfigurationChanged(Configuration configuration) {
        super.onConfigurationChanged(configuration);
        LocalManageUtil.getInstance().onConfigurationChanged(getApplicationContext());
    }

    /* access modifiers changed from: protected */
    public void attachBaseContext(Context context) {
        super.attachBaseContext(LocalManageUtil.getInstance().updateLocal(context));
    }

    public void onCreate() {
        super.onCreate();
        //SplitCompat.installActivity(this);
        OneSignal.setLogLevel(OneSignal.LOG_LEVEL.VERBOSE, OneSignal.LOG_LEVEL.NONE);

        // OneSignal Initialization
        OneSignal.startInit(this)
                .inFocusDisplaying(OneSignal.OSInFocusDisplayOption.Notification)
                .unsubscribeWhenNotificationsAreDisabled(true)
                .init();

        Get_All_ID.LoadAppData_Application(getApplicationContext(),getPackageName());

        Google_Intertitial.Load_Google_Intertitial( getApplicationContext() );

//        Google_Intertitial_Creation.Load_Google_Intertitial( getApplicationContext() );
//        Google_Intertitial_Creation_back.Load_Google_Intertitial( getApplicationContext() );
//        Google_Intertitial_SAve.Load_Google_Intertitial( getApplicationContext() );

        loadAdaptiveBanner(getApplicationContext());


        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true);
        if (!ProcessUtils.isMetricaProcess(this)) {

                LocalManageUtil.getInstance().setApplicationLanguage(this);
//                initFabric();
                initDebug();
                getDetectionEngine();
                initDB();
//                Analytics.init(this);

                registerActivityLifecycleCallbacks(this);
//                initCloudSync();
//                initWakeupLib();
//                BillingManager.init(this);
                ImageStorageUtils.clearAllTempFolders();
            }
        
    }

    AdView adView;

    private void loadAdaptiveBanner( Context context) {

        adView = new AdView(this);
        adView.setAdUnitId( Utils.Google_Banner);
//        AdRequest adRequest = new AdRequest.Builder().addTestDevice(AdRequest.DEVICE_ID_EMULATOR).build();
        AdRequest adRequest = new AdRequest.Builder().build();


        AdSize adSize = getAdSize();
        adView.setAdSize(adSize);

        adView.loadAd(adRequest);
    }


    public void adaptiveBannerView(LinearLayout layAd) {

        if (adView.getParent() != null) {
            ViewGroup tempVg = (ViewGroup) adView.getParent();
            tempVg.removeView(adView);
        }
        layAd.addView(adView);
    }
    private AdSize getAdSize() {
//        DisplayMetrics displayMetrics = new DisplayMetrics();
//        ((WindowManager) this.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getMetrics(displayMetrics);



        DisplayMetrics dm = getResources().getDisplayMetrics();



        float widthPixels = dm.widthPixels;
        float density = dm.density;

        int adWidth = (int) (widthPixels / density);

        return AdSize.getCurrentOrientationAnchoredAdaptiveBannerAdSize(this, adWidth);
    }





    private void initDB() {
        DBManager.init(getApplicationContext());
    }





    public static PolygonDetectEngineInterface getDetectionEngine() {
        if (gDetectionEngine == null) {
            gDetectionEngine = new PolygonDetectEngineInterface();
            gDetectionEngine.CreateEngine();
        }
        return gDetectionEngine;
    }

    public static ImageHolder imageHolder() {
        if (imageHolder == null) {
            imageHolder = ImageHolder.get();
        }
        return imageHolder;
    }



    public static UserRepository userRepo() {
        if (userRepo == null) {
            userRepo = UserRepository.get();
        }
        return userRepo;
    }

    public void onActivityCreated(Activity activity, Bundle bundle) {
//        Crashlytics.setString("screen_created", activity.getComponentName().getClassName());
        Timber.tag(LOG_TAG).w("onActivityCreated %s", activity);
        if (activity instanceof SplashActivity) {
            this.mLoaderCallback = new BaseLoaderCallback(activity) {
                public void onManagerConnected(int status) {
                    if (status != 0) {
                        super.onManagerConnected(status);
                    }
                }
            };
            if (!OpenCVLoader.initDebug()) {
                OpenCVLoader.initAsync("4.0.1", activity, this.mLoaderCallback);
            } else {
                this.mLoaderCallback.onManagerConnected(0);
            }
        }
    }

    public void onActivityStarted(Activity activity) {
//        Crashlytics.setString("screen_started", activity.getComponentName().getClassName());
        Timber.tag(LOG_TAG).w("onActivityStarted %s", activity);
//        if (!(activity instanceof SplashActivity) && !userRepo().isUserPremium(activity) ) {
//            g_wakeupLib.onStart();
//        }
    }

    public void onActivityResumed(Activity activity) {
//        if (!(activity instanceof SplashActivity) && !userRepo().isUserPremium(activity) ) {
//            g_wakeupLib.onResumeFragments(activity);
//        }
    }

    public void onActivityPaused(Activity activity) {
//        if (!(activity instanceof SplashActivity) && !userRepo().isUserPremium(activity) ) {
//            g_wakeupLib.onPause();
//        }
    }

    public void onActivityStopped(Activity activity) {
        Timber.tag(LOG_TAG).w("onActivityStopped %s", activity);
    }
}
