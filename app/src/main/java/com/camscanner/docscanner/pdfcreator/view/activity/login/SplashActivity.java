package com.camscanner.docscanner.pdfcreator.view.activity.login;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.Nullable;

import com.ru.admaster.Google_Native_Preload;

import com.camscanner.docscanner.pdfcreator.R;
import com.camscanner.docscanner.pdfcreator.view.activity.BaseMainActivity;
import com.camscanner.docscanner.pdfcreator.view.activity.main.MainActivity;

public class SplashActivity extends BaseMainActivity {


    public static void reStart(Context context) {
        Intent intent = new Intent(context, SplashActivity.class);
        intent.setFlags(268468224);
        context.startActivity(intent);
    }


    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags( WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN );
        requestWindowFeature( Window.FEATURE_NO_TITLE );

        setContentView( R.layout.activity_splash );

        Google_Native_Preload.Load_Native_Ad(SplashActivity.this);
//        Google_Native_Banner_Preload.Load_Native_Banner(SpleshActivity.this);


        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                SplashActivity.this.startActivity(new Intent(SplashActivity.this, MainActivity.class));
                SplashActivity.this.finish();
            }
        }, 2000);





    }

}
