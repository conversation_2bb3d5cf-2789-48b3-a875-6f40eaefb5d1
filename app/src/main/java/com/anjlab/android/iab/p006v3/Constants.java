package com.anjlab.android.iab.p006v3;

public class Constants {

    @Deprecated
    public static final int BILLING_ERROR_INVALID_DEVELOPER_PAYLOAD = 105;


    public static final String PRODUCT_TYPE_MANAGED = "inapp";
    public static final String PRODUCT_TYPE_SUBSCRIPTION = "subs";
    public static final String RESPONSE_AUTO_RENEWING = "autoRenewing";
    public static final String RESPONSE_CODE = "RESPONSE_CODE";
    public static final String RESPONSE_DESCRIPTION = "description";
    public static final String RESPONSE_DEVELOPER_PAYLOAD = "developerPayload";
    public static final String RESPONSE_FREE_TRIAL_PERIOD = "freeTrialPeriod";
    public static final String RESPONSE_INAPP_SIGNATURE = "INAPP_DATA_SIGNATURE";
    public static final String RESPONSE_INTRODUCTORY_PRICE = "introductoryPrice";
    public static final String RESPONSE_INTRODUCTORY_PRICE_CYCLES = "introductoryPriceCycles";
    public static final String RESPONSE_INTRODUCTORY_PRICE_MICROS = "introductoryPriceAmountMicros";
    public static final String RESPONSE_INTRODUCTORY_PRICE_PERIOD = "introductoryPricePeriod";
    public static final String RESPONSE_ORDER_ID = "orderId";
    public static final String RESPONSE_PACKAGE_NAME = "packageName";
    public static final String RESPONSE_PRICE = "price";
    public static final String RESPONSE_PRICE_CURRENCY = "price_currency_code";
    public static final String RESPONSE_PRICE_MICROS = "price_amount_micros";
    public static final String RESPONSE_PRODUCT_ID = "productId";

    public static final String RESPONSE_SUBSCRIPTION_PERIOD = "subscriptionPeriod";
    public static final String RESPONSE_TITLE = "title";
    public static final String RESPONSE_TYPE = "type";
}
